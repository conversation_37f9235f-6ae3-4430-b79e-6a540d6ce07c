'use client';

import { useFormContext } from 'react-hook-form';

const categories = [
  'Consultation in the out-patients department (including medico-legal work)',
  'Consultation and minor procedures in the out-patients department',
  'Consultation and minor procedures in the out-patients department plus admission of in/day-patients',
  'Consultation and minor procedures in the outpatient department plus admission and operative procedures (Declared Procedures) on in/day-patients',
  'Admission and care of in/day patients only',
  'Provision of anaesthetic services',
  'Diagnostic and therapeutic imaging procedures and reporting and provision of advice to colleagues including admission and interventional procedures',
  'Diagnostic and therapeutic imaging procedures, reporting of results and provision of advice to colleagues',
  'Pathology procedures and reporting and provision of advice to colleagues',
  'Administration of sedation by consultants other than Anaesthetists',
];

export default function StepPrivileges() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Tick category of Practising Privileges for which you are applying
        </p>
        <div className="space-y-4">
          {categories.map((cat, i) => (
            <div
              key={i}
              className="p-4 rounded-lg border hover:bg-muted/50 transition-colors"
            >
              <div className="text-sm font-medium leading-relaxed mb-3">
                {cat}
              </div>
              <div className="flex space-x-6 ml-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register(`privileges.${i}.adults`)}
                    className="h-4 w-4 rounded border-input text-primary focus:ring-primary focus:ring-offset-0"
                  />
                  <label className="text-sm cursor-pointer">Adults</label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register(`privileges.${i}.paediatrics`)}
                    className="h-4 w-4 rounded border-input text-primary focus:ring-primary focus:ring-offset-0"
                  />
                  <label className="text-sm cursor-pointer">Paediatric</label>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
