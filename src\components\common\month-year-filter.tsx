'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useState, useEffect } from 'react';

interface MonthYearFilterProps {
  onFilterChange?: (month: string, year: string) => void;
  className?: string;
  selectedMonth?: string;
  selectedYear?: string;
  defaultToCurrentDate?: boolean;
}

const MonthYearFilter: React.FC<MonthYearFilterProps> = ({
  onFilterChange,
  className = 'flex gap-2',
  selectedMonth: externalSelectedMonth,
  selectedYear: externalSelectedYear,
  defaultToCurrentDate = true,
}) => {
  const currentDate = new Date();
  const currentMonth = (currentDate.getMonth() + 1).toString();
  const currentYear = currentDate.getFullYear().toString();

  // Use external values if provided, otherwise use internal state
  const [internalSelectedMonth, setInternalSelectedMonth] = useState<string>(
    defaultToCurrentDate ? currentMonth : ''
  );
  const [internalSelectedYear, setInternalSelectedYear] = useState<string>(
    defaultToCurrentDate ? currentYear : ''
  );

  // Determine which values to use
  const selectedMonth = externalSelectedMonth !== undefined ? externalSelectedMonth : internalSelectedMonth;
  const selectedYear = externalSelectedYear !== undefined ? externalSelectedYear : internalSelectedYear;

  const months = [
    { value: '1', label: 'January' },
    { value: '2', label: 'February' },
    { value: '3', label: 'March' },
    { value: '4', label: 'April' },
    { value: '5', label: 'May' },
    { value: '6', label: 'June' },
    { value: '7', label: 'July' },
    { value: '8', label: 'August' },
    { value: '9', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' },
  ];

  const startYear = 2025;
  const years = Array.from(
    { length: currentDate.getFullYear() - startYear + 1 },
    (_, i) => {
      const year = startYear + i;
      return { value: year.toString(), label: year.toString() };
    }
  );

  // Handle month change
  const handleMonthChange = (month: string) => {
    if (externalSelectedMonth === undefined) {
      setInternalSelectedMonth(month);
    }
    onFilterChange?.(month, selectedYear);
  };

  // Handle year change
  const handleYearChange = (year: string) => {
    if (externalSelectedYear === undefined) {
      setInternalSelectedYear(year);
    }
    onFilterChange?.(selectedMonth, year);
  };

  useEffect(() => {
    // Only call onFilterChange on mount if we have default values and they're not empty
    if (defaultToCurrentDate && selectedMonth && selectedYear) {
      onFilterChange?.(selectedMonth, selectedYear);
    }
  }, [onFilterChange, defaultToCurrentDate]); // Only run on mount

  return (
    <div className={className}>
      <Select value={selectedMonth} onValueChange={handleMonthChange}>
        <SelectTrigger className="w-[130px]">
          <SelectValue placeholder="Month" />
        </SelectTrigger>
        <SelectContent>
          {months.map((month) => (
            <SelectItem key={month.value} value={month.value}>
              {month.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={selectedYear} onValueChange={handleYearChange}>
        <SelectTrigger className="w-[100px]">
          <SelectValue placeholder="Year" />
        </SelectTrigger>
        <SelectContent>
          {years.map((year) => (
            <SelectItem key={year.value} value={year.value}>
              {year.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default MonthYearFilter;
