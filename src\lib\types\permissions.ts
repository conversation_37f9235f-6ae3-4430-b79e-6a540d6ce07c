'use client';

import { GetProfile } from '@/api/staff';

export const PERMISSIONS = {
  DASHBOARD_VIEW: 'dashboard:view',

  PACKAGE_VIEW: 'package:view',
  PACKAGE_CREATE: 'package:create',
  PACKAGE_EDIT: 'package:edit',
  PACKAGE_DELETE: 'package:delete',

  BOOKING_VIEW: 'booking:view',
  BOOKING_UPDATE: 'booking:update',

  LOCATION_CREATE: 'location:create',
  LOCATION_EDIT: 'location:edit',
  LOCATION_DELETE: 'location:delete',
  LOCATION_REGION: 'location:region',
  LOCATION_ALL: 'location:all',

  INCIDENT_CREATE: 'incident:create',
  INCIDENT_VIEW: 'incident:view',
  INCIDENT_EDIT: 'incident:edit',
  INCIDENT_SUBMIT: 'incident:submit',
  INCIDENT_APPROVE: 'incident:approve',
  INCIDENT_DELETE: 'incident:delete',
  INCIDENT_COMMENT: 'incident:comment',
  INCIDENT_ASSIGN: 'incident:assign',
  INCIDENT_CLOSE: 'incident:close',

  REWARD_VIEW: 'reward:view',
  REWARD_CREATE: 'reward:create',
  REWARD_EDIT: 'reward:edit',
  REWARD_DELETE: 'reward:delete',

  FEEDBACK_VIEW: 'feedback:view',

  REFERRAL_VIEW: 'referral:view',
  REFERRAL_CREATE: 'referral:create',
  REFERRAL_EDIT: 'referral:edit',
  REFERRAL_DELETE: 'referral:delete',

  TRANSACTION_VIEW: 'transaction:view',
  TRANSACTION_CREATE: 'transaction:create',
  TRANSACTION_EDIT: 'transaction:edit',

  STAFF_CREATE: 'staff:create',
  STAFF_VIEW: 'staff:view',
  STAFF_EDIT: 'staff:edit',
  STAFF_EDIT_OWN: 'staff:edit_own',
  STAFF_DELETE: 'staff:delete',

  SETTINGS_VIEW: 'settings:view',
  SETTINGS_CREATE: 'settings:create',
  SETTINGS_EDIT: 'settings:edit',
  SETTINGS_DELETE: 'settings:delete',

  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_EDIT: 'role:edit',
  ROLE_DELETE: 'role:delete',

  PERMISSION_VIEW: 'permission:view',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_EDIT: 'permission:edit',
  PERMISSION_DELETE: 'permission:delete',

  CAFETERIA_VIEW: 'cafeteria:view',
  CAFETERIA_INVENTORY_MANAGE: 'cafeteria:inventory_manage',
  CAFETERIA_MENU_MANAGE: 'cafeteria:menu_manage',
  CAFETERIA_MENU_SCHEDULE: 'cafeteria:menu_schedule',
  CAFETERIA_ORDERS_MANAGE: 'cafeteria:orders_manage',
  CAFETERIA_POS_ACCESS: 'cafeteria:pos_access',
  CAFETERIA_SPECIAL_APPROVE: 'cafeteria:special_approve',

  CHIS_VIEW: 'chis:view',
  CHIS_CREATE: 'chis:create',
  CHIS_EDIT: 'chis:edit',
  CHIS_APPROVE: 'chis:approve',
  CHIS_GENERATE_CODE: 'chis:generate_code',

  HUB_VIEW: 'hub:view',
  HUB_CREATE: 'hub:create',
  HUB_EDIT: 'hub:edit',

  PROCESS_VIEW: 'process:view',
  PROCESS_CREATE: 'process:create',
  PROCESS_EDIT: 'process:edit',

  AI_ASSISTANT_VIEW: 'ai_assistant:view',
  AI_ASSISTANT_CHAT: 'ai_assistant:chat',
};

export function hasPermission(permission: string): boolean {
  const { profile, isLoading } = GetProfile();

  if (isLoading) {
    return true;
  }

  const roles =
    profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

  if (!profile || !profile.data || !roles.length) {
    console.warn(
      'Profile or roles not available for permission check:',
      permission
    );
    return false;
  }

  return roles.some((role: { permissions: { action: string }[] }) =>
    role?.permissions?.some(
      (item: { action: string }) => item.action === permission
    )
  );
}

export const profileDetails = () => {
  const { profile, isLoading } = GetProfile();
  if (profile && profile.data) {
    return profile.data;
  }
};

export function isProfileLoaded(): boolean {
  const { profile, isLoading, error } = GetProfile();

  if (isLoading) return true;
  `
  // Only return false if we have a definitive negative result
  // (profile loaded but invalid or error occurred)`;
  if (error) return false;
  if (profile === undefined) return true;

  if (
    profile &&
    (!profile.data ||
      ((!profile.data.roles || profile.data.roles.length === 0) &&
        !profile.data.role))
  )
    return false;

  return true;
}
