'use client';

import { io, Socket } from 'socket.io-client';
import { notificationStore } from '@/store/notificationStore';
import { SOCKET_EVENTS, SOCKET_CONFIG } from '@/lib/socket-config';
import { accessTokenStore } from '@/store/accessToken';

// Types for socket service
type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'offline';
type PresenceStatus = 'online' | 'offline' | 'away' | 'busy';

interface SocketState {
  socket: Socket | null;
  isConnected: boolean;
  connectionAttempted: boolean;
  reconnectAttempts: number;
}

// Modern functional approach with state management
const createSocketService = (url: string) => {
  const maxReconnectAttempts = 3;

  let state: SocketState = {
    socket: null,
    isConnected: false,
    connectionAttempted: false,
    reconnectAttempts: 0,
  };

  // Enhanced logging utility
  const log = {
    info: (message: string, data?: any) => {
      console.log(`🔌 [Socket.IO] ${message}`, data || '');
    },
    warn: (message: string, data?: any) => {
      console.warn(`⚠️ [Socket.IO] ${message}`, data || '');
    },
    error: (message: string, data?: any) => {
      console.error(`❌ [Socket.IO] ${message}`, data || '');
    },
    success: (message: string, data?: any) => {
      console.log(`✅ [Socket.IO] ${message}`, data || '');
    },
    debug: (message: string, data?: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug(`🐛 [Socket.IO] ${message}`, data || '');
      }
    },
  };

  const connect = () => {
    if (state.socket?.connected) {
      log.info('Already connected', { socketId: state.socket.id });
      return;
    }

    if (
      state.connectionAttempted &&
      state.reconnectAttempts >= maxReconnectAttempts
    ) {
      log.error('Max reconnection attempts reached. Running in offline mode.');
      return;
    }

    state.connectionAttempted = true;

    // Get the current token from the store
    const token = accessTokenStore.accessToken;
    const hasToken = !!token;

    log.info('Attempting to connect...', {
      url,
      hasToken,
      attempt: state.reconnectAttempts + 1,
      maxAttempts: maxReconnectAttempts,
    });

    const config = {
      ...SOCKET_CONFIG,
      auth: {
        token: token || 'no-token',
      },
    };

    state.socket = io(url, config);

    state.socket.on('connect', () => {
      log.success('Connected successfully', {
        socketId: state.socket?.id,
        transport: state.socket?.io.engine.transport.name,
      });
      state.isConnected = true;
      state.reconnectAttempts = 0; // Reset on successful connection
    });

    state.socket.on('disconnect', (reason) => {
      log.warn('Disconnected', { reason, socketId: state.socket?.id });
      state.isConnected = false;
    });

    state.socket.on('connect_error', (error) => {
      state.reconnectAttempts++;
      log.error('Connection failed', {
        error: error.message,
        attempt: state.reconnectAttempts,
        maxAttempts: maxReconnectAttempts,
      });

      if (state.reconnectAttempts >= maxReconnectAttempts) {
        log.error('Running in offline mode - real-time features disabled');
        state.socket?.disconnect();
      }
    });

    // Log transport upgrades
    state.socket.on('upgrade', () => {
      log.info('Transport upgraded', {
        transport: state.socket?.io.engine.transport.name,
      });
    });

    // Set up notification listeners
    setupNotificationListeners();
  };

  const setupNotificationListeners = () => {
    if (!state.socket) return;

    // General notification handler - now includes backend sync trigger
    state.socket.on(SOCKET_EVENTS.NOTIFICATION, (data: any) => {
      // Add to local store for immediate display
      notificationStore.addNotification({
        title: data.title,
        message: data.message,
        link: data.link,
        type: data.type,
        metadata: {
          ...data.data, // Backend 'data' field
          priority: data.priority,
          source: 'websocket',
        },
      });

      // Trigger a backend sync if this is a persistent notification
      if (data.persistent !== false) {
        // The useNotifications hook will handle the backend sync
        // This ensures the notification is properly saved and synced across devices
      }
    });

    // Backend notification sync event
    state.socket.on(SOCKET_EVENTS.NOTIFICATION_SYNC, () => {
      // Trigger a refresh of notifications from backend
      // This will be handled by the useNotifications hook
      window.dispatchEvent(new CustomEvent('notification-sync-requested'));
    });


  };

  const disconnect = () => {
    if (state.socket) {
      log.info('Disconnecting manually', { socketId: state.socket.id });
      state.socket.disconnect();
      state.socket = null;
    }
    state.isConnected = false;
    state.connectionAttempted = false;
    state.reconnectAttempts = 0;
    log.info('Disconnected and state reset');
  };

  // Reconnect with updated authentication token
  const reconnectWithAuth = () => {
    log.info('Reconnecting with updated authentication...');
    disconnect();
    connect();
  };

  const emit = (event: string, data: any) => {
    if (state.socket?.connected) {
      log.debug('Emitting event', { event, data });
      state.socket.emit(event, data);
    } else {
      // Only log for important events, not routine ones
      if (!['status_update'].includes(event)) {
        log.warn('Socket not connected - event not sent', { event, data });
      }
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    state.socket?.on(event, callback);
  };

  const off = (event: string, callback: (...args: any[]) => void) => {
    state.socket?.off(event, callback);
  };

  const updatePresence = (status: PresenceStatus) => {
    emit(SOCKET_EVENTS.UPDATE_PRESENCE, { status });
  };

  const isConnectedToServer = (): boolean => {
    return state.isConnected && state.socket?.connected === true;
  };

  const isOfflineMode = (): boolean => {
    return (
      state.connectionAttempted &&
      state.reconnectAttempts >= maxReconnectAttempts
    );
  };

  const getConnectionStatus = (): ConnectionStatus => {
    if (state.isConnected) return 'connected';
    if (isOfflineMode()) return 'offline';
    if (state.connectionAttempted) return 'connecting';
    return 'disconnected';
  };

  // Debug utilities for console inspection
  const getDebugInfo = () => {
    const debugInfo = {
      status: getConnectionStatus(),
      isConnected: state.isConnected,
      socketId: state.socket?.id || null,
      transport: state.socket?.io?.engine?.transport?.name || null,
      url,
      reconnectAttempts: state.reconnectAttempts,
      maxReconnectAttempts,
      hasToken: !!accessTokenStore.accessToken,
      connectionAttempted: state.connectionAttempted,
      timestamp: new Date().toISOString(),
    };

    log.info('Debug Info', debugInfo);
    return debugInfo;
  };

  const logStatus = () => {
    const status = getConnectionStatus();
    const statusEmoji = {
      connected: '🟢',
      connecting: '🟡',
      disconnected: '🔴',
      offline: '⚫',
    };

    console.log(
      `${statusEmoji[status]} Socket.IO Status: ${status.toUpperCase()}`
    );
    return getDebugInfo();
  };

  // Return the public API
  return {
    connect,
    disconnect,
    reconnectWithAuth,
    emit,
    on,
    off,
    updatePresence,
    isConnectedToServer,
    isOfflineMode,
    getConnectionStatus,
    // Debug utilities
    getDebugInfo,
    logStatus,
  };
};

// Create a singleton instance
// Replace with your actual Socket.IO server URL
const socketService = createSocketService(
  process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000'
);

// Global debugging utilities for browser console
if (typeof window !== 'undefined') {
  // Make socket service available globally for debugging
  (window as any).socketDebug = {
    service: socketService,
    status: () => socketService.logStatus(),
    info: () => socketService.getDebugInfo(),
    connect: () => socketService.connect(),
    disconnect: () => socketService.disconnect(),
    reconnect: () => socketService.reconnectWithAuth(),
    // Quick status check
    check: () => {
      const status = socketService.getConnectionStatus();
      console.log(`🔌 Socket Status: ${status}`);
      return status;
    },
    // Monitor connection for a period
    monitor: (seconds: number = 30) => {
      console.log(`🔍 Monitoring socket for ${seconds} seconds...`);
      const interval = setInterval(() => {
        socketService.logStatus();
      }, 2000);

      setTimeout(() => {
        clearInterval(interval);
        console.log('🔍 Monitoring stopped');
      }, seconds * 1000);

      return interval;
    },
  };

  // Log initial status
  console.log('🔌 Socket.IO Debug utilities available at window.socketDebug');
  console.log('📋 Available commands:');
  console.log('  • window.socketDebug.status() - Show current status');
  console.log('  • window.socketDebug.info() - Show detailed info');
  console.log('  • window.socketDebug.check() - Quick status check');
  console.log('  • window.socketDebug.connect() - Force connect');
  console.log('  • window.socketDebug.disconnect() - Force disconnect');
  console.log('  • window.socketDebug.reconnect() - Reconnect with auth');
  console.log('  • window.socketDebug.monitor(30) - Monitor for 30 seconds');
}

export default socketService;
