'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Download, Eye, Loader2, Pencil } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DocumentPreviewProps {
  id: any;
  documentPath: string;
  currentStatus?: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED';
  updateStaff: (updates: Record<string, any>) => Promise<void>;
}

export function PrivilegingPreview({
  id,
  documentPath,
  currentStatus = 'SUBMITTED',
  updateStaff,
}: DocumentPreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [makeComment, setMakeComment] = useState(false);
  const [comment, setComment] = useState('');
  const [status, setStatus] = useState<
    'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | ''
  >('');

  const isPdf = /\.pdf$/i.test(documentPath);

  useEffect(() => {
    let objectUrl: string | null = null;

    if (isOpen && isPdf) {
      setIsLoading(true);
      setError(null);
      fetch(documentPath)
        .then((res) => {
          if (!res.ok) {
            throw new Error('Failed to fetch file for preview');
          }
          return res.blob();
        })
        .then((blob) => {
          objectUrl = URL.createObjectURL(blob);
          setFileUrl(objectUrl);
        })
        .catch((err) => {
          console.error('Error fetching invoice:', err);
          setError('Could not load preview.');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
        setFileUrl(null);
      }
    };
  }, [isOpen, isPdf, documentPath]);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = fileUrl || documentPath; // Use blob URL if available
    link.download = documentPath.split('/').pop() || 'document';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleUpdateStatus = async () => {
    await updateStaff({
      privilegingDocumentStatus: status,
      privilegingDocumentRemarks: comment.trim(),
    });
  };

  return (
    <>
      <Button
        size="sm"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 mb-1"
      >
        <Eye className="h-4 w-4" />
        View Privileging Application
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex gap-2">
              Privileging Application{' '}
              {!makeComment && (
                <span>
                  <Download
                    onClick={handleDownload}
                    className="h-5 w-5 text-gray-500 hover:text-gray-800 hover:cursor-pointer"
                  />
                </span>
              )}
              {currentStatus !== 'APPROVED' && currentStatus !== 'REJECTED' && (
                <span>
                  <Pencil
                    onClick={() => setMakeComment(!makeComment)}
                    className="h-5 w-5 text-gray-500 hover:text-gray-800 hover:cursor-pointer"
                  />
                </span>
              )}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto flex justify-center items-center min-h-[70vh]">
            {makeComment ? (
              <div className="w-full max-w-2xl space-y-4 p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={status}
                      onValueChange={(value) =>
                        setStatus(
                          value as
                            | 'SUBMITTED'
                            | 'UNDER_REVIEW'
                            | 'APPROVED'
                            | 'REJECTED'
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Change status" />
                      </SelectTrigger>
                      <SelectContent>
                        {currentStatus === 'SUBMITTED' && (
                          <SelectItem value="UNDER_REVIEW">
                            Under Review
                          </SelectItem>
                        )}
                        {currentStatus === 'UNDER_REVIEW' && (
                          <>
                            <SelectItem value="APPROVED">Approved</SelectItem>
                            <SelectItem value="REJECTED">Rejected</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="comment">Comment</Label>
                  <Textarea
                    id="comment"
                    placeholder="Add your comment here..."
                    value={comment}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                      setComment(e.target.value)
                    }
                    className="min-h-[100px]"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setMakeComment(false)}
                  >
                    Cancel
                  </Button>
                  <Button size="sm" onClick={handleUpdateStatus}>
                    Submit
                  </Button>
                </div>
              </div>
            ) : (
              <>
                {isLoading && <Loader2 className="h-8 w-8 animate-spin" />}
                {error && <p className="text-destructive">{error}</p>}
                {!isLoading && !error && (
                  <>
                    {isPdf && fileUrl && (
                      <iframe
                        src={fileUrl}
                        className="w-full h-[70vh]"
                        title="Invoice PDF"
                      />
                    )}
                    {!isPdf && (
                      <div className="text-center py-8">
                        <p>Preview not available for this file type.</p>
                        <Button onClick={handleDownload} className="mt-4">
                          Download File
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
