import useSWR, { SWRConfiguration, Key } from 'swr';
import { useSnapshot } from 'valtio';
import { accessTokenStore } from '@/store/accessToken';
import { fetcher } from '@/api/fetcher';

// Network detection utility
const isOnline = () => {
  return typeof navigator !== 'undefined' ? navigator.onLine : true;
};

type UseAuthSWRReturn<Data = any, Error = any> = {
  data?: Data;
  error?: Error;
  isLoading: boolean;
  mutate: () => Promise<Data | undefined>;
};

export function useAuthSWR<Data = any, Error = any>(
  key: Key, // e.g. '/profile'
  config?: SWRConfiguration
): UseAuthSWRReturn<Data, Error> {
  const snap = useSnapshot(accessTokenStore);
  const accessToken = snap.accessToken;

  // If no token or no network, skip fetch by passing null key to SWR
  const swrKey = accessToken && isOnline() ? key : null;

  const { data, error, isLoading, mutate } = useSWR<Data, Error>(
    swrKey,
    fetcher,
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      // Don't retry when offline
      shouldRetryOnError: () => isOnline(),
      // Increase error retry interval when offline
      errorRetryInterval: isOnline() ? 5000 : 30000,
      ...config,
    }
  );

  return { data, error, isLoading, mutate };
}
