import { useAuthSWR } from '../useAuthSWR';
// import useSWR from 'swr';

export const GetAllReferrals = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/referral/list-all-referral?${qs.toString()}`
  );

  return {
    referrals: data,
    referralLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllReferringEntites = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/referral/list-referring-entity?${qs.toString()}`
  );

  return {
    referrers: data,
    referrerLoading: isLoading,
    mutate: mutate,
  };
};

export const GetReferralDetails = (id: string) => {
  const { data, error, isLoading, mutate } = useAuthSWR(
    id ? `/referral/details/${id}` : null
  );

  return {
    referralDetails: data,
    detailsLoading: isLoading,
    detailsError: error,
    mutate: mutate,
  };
};

export const GetPublicEntity = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/referral/list-public-entity?${qs.toString()}`
  );

  return {
    entity: data,
    entityLoading: isLoading,
    mutate: mutate,
  };
};

export const GetSpecialty = () => {
  const { data, isLoading, mutate } = useAuthSWR(`/staff/list-specialty`);

  return {
    specialty: data,
    specialtyLoading: isLoading,
    mutate: mutate,
  };
};

export const GetConsultant = () => {
  const { data, isLoading } = useAuthSWR(`/staff/list-consultant`);

  return {
    doctor: data,
    doctorLoading: isLoading,
  };
};