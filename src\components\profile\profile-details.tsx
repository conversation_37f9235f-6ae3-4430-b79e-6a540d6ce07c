'use client';

import React, { use, useState } from 'react';
import { Modal } from '@/components/common/modal';
import { UserProps } from './types';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  User,
  Calendar,
  Mail,
  Shield,
  Clock,
  Phone,
  MapPin,
  Building,
  Wallet,
  CreditCard,
  Copy,
  FileText,
} from 'lucide-react';
import dayjs from 'dayjs';
import { formatRoleNames, numberFormat } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { WithdrawModal } from './withdraw-modal';
import { ClearCreditModal } from './clear-credit-modal';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { Input } from '@/components/ui/input';
import { myApi } from '@/api/fetcher';
import { timestamp, generateCode } from '@/lib/utils';

const PaystackButtonDynamic = dynamic(
  () => import('react-paystack').then((mod) => mod.PaystackButton),
  {
    ssr: false,
  }
);

interface ProfileDetailsProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: UserProps;
}

const ProfileDetails: React.FC<ProfileDetailsProps> = ({
  open,
  setOpen,
  user,
}) => {
  const [withdrawModalOpen, setWithdrawModalOpen] = useState(false);
  const [clearCreditModalOpen, setClearCreditModalOpen] = useState(false);
  const [editingMealVoucher, setEditingMealVoucher] = useState(false);
  const [mealVoucherAmount, setMealVoucherAmount] = useState(0);
  const [mealVoucherDisplay, setMealVoucherDisplay] = useState('');

  if (!user) return null;

  console.log(user);

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A';
    return dayjs(date).format('MMM DD, YYYY hh:mm A');
  };

  //   const addDecimals = (num: number): string => {
  //   return (Math.round(num * 100) / 100).toFixed(2);
  // };

  // const paystackCharges = addDecimals(
  // mealVoucherAmount <= 2500 ? Number((mealVoucherAmount * 0.015).toFixed(2)) : Number((mealVoucherAmount * 0.015 + 100).toFixed(2))
  // )
  // const totalPaystackCharges = Number(paystackCharges) < 2000 ? paystackCharges : 2000

  const publicKey = process.env.NEXT_PUBLIC_PAYSTACKPUBLICK || '';
  const subaccount = process.env.NEXT_PUBLIC_PAYSTACKSUBACCOUNT || '';
  const amount = Number(mealVoucherAmount) * 100;
  const email = user.email;
  const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

  const handleCreditSuccess = () => {
    setEditingMealVoucher(false);
    setMealVoucherAmount(0);
    setMealVoucherDisplay('');
    setOpen(false);
  };

  const handlePaystackSuccess = async () => {
    const payload: any = {
      staffCode: user.staffCode,
      amount: mealVoucherAmount,
      mode: "paystack",
      reference: reference,
      creditedBy: "Self via paystack",
    };
    const res = await myApi.post('/staff/pay-credit', payload);
    if (res.status === 200) {
      toast.success(res.data.data.message);
      handleCreditSuccess();
    } else {
      console.error('Something went wrong');
    }
  };

  const componentProps = {
    reference: reference,
    subaccount,
    email,
    amount,
    publicKey,
    text: `MAKE PAYMENT`,
    onSuccess: handlePaystackSuccess,
    onClose: () => alert('You can also visit the cafeteria to make payment.'),
  };

  return (
    <Modal
      open={open}
      setOpen={(isOpen) => {
        if (!isOpen) {
          setEditingMealVoucher(false);
          setMealVoucherAmount(0);
          setMealVoucherDisplay('');
        }
        setOpen(isOpen);
      }}
      title="Profile Details"
      description="Your complete account and profile information"
      size="lg"
    >
      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal & Professional
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Account & Financial
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information Column */}
            <div className="space-y-4 text-sm">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Personal Information
              </h3>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Full Name
                </span>
                <span className="font-semibold">{user.fullName || 'N/A'}</span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Email Address
                </span>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{user.email || 'N/A'}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="font-medium text-muted-foreground">
                  Phone Number
                </span>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{user.phoneNumber || 'N/A'}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="font-medium text-muted-foreground">
                  Referral Code
                </span>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="px-3 py-1 text-sm">
                    {user.referralCode?.code || 'N/A'}
                  </Badge>
                  {user.referralCode?.code && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          user.referralCode?.code || ''
                        );
                        toast.success('Referral code copied to clipboard');
                      }}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="font-medium text-muted-foreground">
                  Role Permissions
                </span>
                <Badge variant="outline" className="w-fit">
                  {formatRoleNames(user.roles)}
                </Badge>
              </div>

              {user.location && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Location
                  </span>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {user.location.name}, {user.location.region}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Professional Information Column */}
            <div className="space-y-4 text-sm">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Professional Information
              </h3>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Staff ID
                </span>
                <span className="font-medium">
                  {user.staffCode || user.staffId || 'N/A'}
                </span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Designation/Role
                </span>
                <span>{user.role || 'N/A'}</span>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Staff Type
                </span>
                <span>{user.type || 'N/A'}</span>
              </div>

              {user.department && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Department
                  </span>
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>{user.department.name}</span>
                  </div>
                </div>
              )}

              {user.unitId && user.unit && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Unit
                  </span>
                  <span>{user.unit.name}</span>
                </div>
              )}

              {user.doctorProfile && (
                <>
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm font-medium text-muted-foreground">
                      Consultant Status
                    </span>
                    <span>
                      {user.doctorProfile.isConsultant
                        ? user.doctorProfile.isVisitingConsultant
                          ? 'Visiting Consultant'
                          : 'Consultant'
                        : 'Doctor'}
                    </span>
                  </div>
                  {user.doctorProfile.specialty && (
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-muted-foreground">
                        Specialty
                      </span>
                      <span>{user.doctorProfile.specialty.name}</span>
                    </div>
                  )}
                  {user.doctorProfile.isConsultant && (
                    <div className="mt-6 pt-4 border-t">
                      <Link href="/privileging-application">
                        <Button
                          onClick={() => setOpen(false)}
                          className="w-full"
                        >
                          <FileText className="w-4 h-4 mr-2" />
                          Practising Privileges
                        </Button>
                      </Link>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="account" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Account Information Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Account Information
              </h3>

              {user?.codeUsage !== undefined && (
                <div className="flex flex-col space-y-1">
                  <span className="text-sm font-medium text-muted-foreground">
                    Referral Code Usage
                  </span>
                  <span className="font-medium">{user.codeUsage} times</span>
                </div>
              )}

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Status
                </span>
                <Badge
                  variant={user.isActive ? 'success' : 'destructive'}
                  className="w-fit"
                >
                  {user.isActive !== undefined
                    ? user.isActive
                      ? 'Active'
                      : 'Inactive'
                    : 'Unknown'}
                </Badge>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Locked
                </span>
                <Badge
                  variant={user.locked ? 'destructive' : 'success'}
                  className="w-fit"
                >
                  {user.locked ? 'Locked' : 'Unlocked'}
                </Badge>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Account Created
                </span>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.createdAt)}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Last Login
                </span>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.lastLogin)}</span>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Last Password Reset
                </span>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDate(user.dateResetPassword)}</span>
                </div>
              </div>
            </div>

            {/* Financial Information Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground mb-3">
                Financial Information
              </h3>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Wallet Balance{' '}
                  {Number(user?.wallet) !== 0 && (
                    <Button
                      className="text-xs"
                      size="sm"
                      disabled={!user?.wallet || Number(user?.wallet) === 0}
                      onClick={() => setWithdrawModalOpen(true)}
                    >
                      Withdraw/Transfer
                    </Button>
                  )}
                </span>
                <div className="flex items-center gap-2">
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold text-green-600">
                    {user?.wallet ? numberFormat(user.wallet) : '₦0.00'}
                  </span>
                </div>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Meal Voucher{' '}
                  <Button
                    size="sm"
                    onClick={() => setEditingMealVoucher(!editingMealVoucher)}
                    className="h-6 px-2 text-xs ml-2"
                  >
                   Credit
                  </Button>
                </span>
                {editingMealVoucher ? (
                  <div className="space-y-2 pr-2">
                    <span className="text-red-400 text-xs">
                      Kindly note that if you have any unpaid credit, it will be
                      automatically cleared.{" "}
                    </span>
                    {user?.unpaidOrderStats?.count > 0 && user?.unpaidOrderStats?.min && (
                      <span className="text-blue-600 text-xs">
                        Minimum required: ₦{user.unpaidOrderStats.min.toLocaleString()}
                      </span>
                    )}
                    <Input
                      type="text"
                      value={mealVoucherDisplay}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^\d]/g, '');
                        const formattedValue = rawValue
                          ? new Intl.NumberFormat('en-US').format(
                              parseInt(rawValue)
                            )
                          : '';
                        setMealVoucherDisplay(formattedValue);
                        setMealVoucherAmount(rawValue ? parseInt(rawValue) : 0);
                      }}
                      placeholder="Enter meal voucher amount"
                      className="w-full"
                    />
                    {mealVoucherAmount > 0 && (() => {
                      const totalBalance = (Number(user?.mealVoucher) || 0) + mealVoucherAmount;
                      const minRequired = user?.unpaidOrderStats?.min || 0;
                      const canClearMin = totalBalance >= minRequired;
                      
                      return canClearMin ? (
                        <PaystackButtonDynamic
                          {...componentProps}
                          className="w-full bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3 rounded-md text-sm font-medium"
                        />
                      ) : (
                        <div className="text-red-500 text-xs p-2 bg-red-50 rounded">
                          Total balance (₦{totalBalance.toLocaleString()}) insufficient to clear minimum credit (₦{minRequired.toLocaleString()})
                        </div>
                      );
                    })()}
                  </div>
                ) : (
                  <span className="font-semibold text-blue-600">
                    {user?.mealVoucher
                      ? numberFormat(user.mealVoucher)
                      : '₦0.00'}
                  </span>
                )}
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Max Allowed Credit
                </span>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">
                    {user?.creditLimit
                      ? numberFormat(user.creditLimit)
                      : '₦0.00'}
                  </span>
                </div>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Unpaid Credit
                </span>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold text-orange-600">
                    {user?.outstandingCredit
                      ? numberFormat(user.outstandingCredit)
                      : '₦0.00'}
                  </span>
                  {user?.unpaidOrderStats?.count > 0 &&
                   <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setClearCreditModalOpen(true)}
                              className="h-6 px-2 text-xs"
                            >
                             Clear Credit
                            </Button>
                  }
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-muted-foreground">
                  Total Earnings
                </span>
                <span className="font-semibold">
                  {user?.total ? numberFormat(user.total) : '₦0.00'}
                </span>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <WithdrawModal
        open={withdrawModalOpen}
        setOpen={setWithdrawModalOpen}
        walletBalance={Number(user?.wallet) || 0}
        setProfileOpen={setOpen}
      />

      <ClearCreditModal
        open={clearCreditModalOpen}
        setOpen={setClearCreditModalOpen}
        outstandingCredit={Number(user?.outstandingCredit) || 0}
        mealVoucherBalance={Number(user?.mealVoucher) || 0}
        walletBalance={Number(user?.wallet) || 0}
        unpaidOrderStats={user?.unpaidOrderStats}
        setProfileOpen={setOpen}
      />
    </Modal>
  );
};

export default ProfileDetails;
