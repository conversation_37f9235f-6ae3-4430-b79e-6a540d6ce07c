'use client';

import { useState } from 'react';
import { CirclePercent } from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { Badge } from '../../ui/badge';
import DiscountCode from '../components/discount-data-table';

export default function DiscountCodePage() {
  const [open, setOpen] = useState(false);

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <CirclePercent className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Manage Discount Codes
      </h2>
      <div className="flex gap-2">
        {hasPermission(PERMISSIONS.REWARD_CREATE) && (
          <Badge
            onClick={() => setOpen(true)}
            className="cursor-pointer py-1.5 px-3"
          >
            New Discount Code
          </Badge>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <DiscountCode openCreate={open} setOpenCreate={setOpen} />
      </div>
    </div>
  );
}
