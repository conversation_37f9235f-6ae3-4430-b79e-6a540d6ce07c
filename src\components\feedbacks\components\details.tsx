import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Star } from 'lucide-react';
import { InvoicePreview } from '@/components/cafeteria/inventory/components/invoice-preview';

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);

  // Update read status when modal is opened
  useEffect(() => {
    const updateReadStatus = async () => {
      if (open && data && !data.read) {
        try {
          setIsLoading(true);
          const res = await myApi.patch('/feedback/mark-as-read', {
            feedbackId: data.id,
          });

          if (res.status === 200) {
            if (mutate) {
              mutate();
            }
          }
        } catch (error) {
          console.error('Error updating read status:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    updateReadStatus();
  }, [open, data, mutate]);

  if (!data) return null;

  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = [];
    const maxRating = 5;

    for (let i = 1; i <= maxRating; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
        />
      );
    }

    return (
      <div className="flex items-center gap-1">
        {stars}
        <span className="ml-2 text-sm font-medium">
          {rating === 1
            ? 'Very Dissatisfied'
            : rating === 2
              ? 'Dissatisfied'
              : rating === 3
                ? 'Neutral'
                : rating === 4
                  ? 'Satisfied'
                  : 'Very Satisfied'}
        </span>
      </div>
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Feedback Details"
      description={`Feedback from ${data.name || 'Anonymous'}`}
    >
      <div className="space-y-6">
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Rating</h3>
          {renderStars(data.rating)}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium">Email</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.email || 'Not provided'}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium">Phone</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.phone || 'Not provided'}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium">Date Submitted</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {dayjs(data.createdAt).format('MMMM D, YYYY h:mm A')}
            </p>
          </div>
          <div>
            {data.imageUrl && (
              <div>
                <InvoicePreview
                  name={`Preview Image`}
                  invoicePath={`${process.env.NEXT_PUBLIC_CEDARCREST_API_BASE}/${data.imageUrl}`}
                />
              </div>
            )}
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium mb-2">Feedback Message</h3>
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
            <p className="text-sm whitespace-pre-wrap">
              {data.feedback || 'No message provided'}
            </p>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Details;
