"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  InputField,
  InputTextArea,
  CustomSelectForm,
  FormRow,
} from "@/components/common/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { myApi } from "@/api/fetcher";
import { referringType } from "@/lib/utils";
import { GetPublicEntity, GetSpecialty, GetConsultant } from "@/api/referral/data";
import { ReferralFormSchema } from "@/components/validations/referral";
import { toast } from "sonner";

interface NewReferralFormProps {
  onBack: () => void;
  props: any;
}

export default function NewReferralForm({
  onBack,
  props,
}: NewReferralFormProps) {
  const { entity } = GetPublicEntity(
    `?location=${props}`
  );
  const entityData = entity?.data;
  const { specialty } = GetSpecialty();
  const { doctor } = GetConsultant();
  const [patientExists, setPatientExists] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [patientData, setPatientData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const specialtyData = specialty?.data;
  const doctorData = doctor?.data;
  console.log(doctorData);

  const form = useForm<z.infer<typeof ReferralFormSchema>>({
    resolver: zodResolver(ReferralFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      email: "",
      phoneNumber: "",
      gender: "",
      referralType: "",
      specialty: "",
      reason: "",
      urgency: "",
      receivingEntity: "",
    },
    mode: "onChange",
  });

  const email = form.watch("email");

  const checkPatientExists = async (email: string) => {
    if (!email) return;
    setIsCheckingEmail(true);
    try {
      const response = await myApi.get(`/referral/check-email?email=${email}`);
      if (response.data) {
        const data = response.data.data;
        form.setValue("firstName", data.firstName || "");
        form.setValue("lastName", data.lastName || "");
        form.setValue(
          "dateOfBirth",
          data.dateOfBirth
            ? new Date(data.dateOfBirth).toISOString().split("T")[0]
            : ""
        );
        form.setValue("phoneNumber", data.phoneNumber || "");
        form.setValue("gender", data.gender || "");
        setPatientData(data);
        setPatientExists(true);
      } else {
        setPatientExists(false);
        setPatientData(null);
      }
    } catch (error) {
      setPatientExists(false);
      setPatientData(null);
    } finally {
      setIsCheckingEmail(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      if (email && email.includes("@")) {
        checkPatientExists(email);
      } else {
        setPatientExists(false);
        setPatientData(null);
        setIsCheckingEmail(false);
      }
    }, 500);
    return () => clearTimeout(timer);
  }, [email]);


  const onSubmit = async (data: any) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      console.log(data);
      let date = new Date(data.dateOfBirth);

      const payload: any = {
        firstName: data.firstName,
        lastName: data.lastName,
        dateOfBirth: date.toISOString(),
        phoneNumber: data.phoneNumber,
        emailAddress: data.email,
        gender: data.gender,
        referralType: data.referralType,
        urgency: data.urgency,
        specialty: data.specialty,
        reasonForReferral: data.reason,
        receivingEntityId: data.receivingEntity,
        referringEntityId: entityData?.referringEntityId,
      };

      const res = await myApi.post(`/referral/new-patient-referral`, payload);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        onBack();
      } else {
        toast.error(res.data.data.message);
      }
    } catch (error: any) {
      console.error('Submission error:', error);
      toast.error(error?.response?.data?.message || 'Failed to submit referral. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mt-6">
      <Card className="col-span-6 md:col-span-2 py-6 md:p-4">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2 -ml-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle className="text-2xl">New Referral</CardTitle>
              <CardDescription>Create a new patient referral</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormRow>
                <CustomSelectForm
                  control={form.control}
                  name="referralType"
                  label="Select Referral Type"
                  placeholder="Select type"
                  options={referringType}
                />
                <CustomSelectForm
                  control={form.control}
                  name="receivingEntity"
                  label="Referred To"
                  placeholder="Select Referral Destination"
                  options={entityData?.referers || []}
                />
                <div className="relative">
                  <InputField
                    control={form.control}
                    name="email"
                    label="Email"
                    placeholder="Enter email address"
                    type="email"
                  />
                  {isCheckingEmail && (
                    <div className="absolute right-3 top-9 text-sm text-blue-600">
                      Checking...
                    </div>
                  )}
                  {patientExists && !isCheckingEmail && (
                    <div className="text-sm text-green-600 mt-1">
                      Patient found - fields auto-populated
                    </div>
                  )}
                </div>
                <InputField
                  control={form.control}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter first name"
                  disabled={patientExists && patientData?.firstName}
                />
                <InputField
                  control={form.control}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter last name"
                  disabled={patientExists && patientData?.lastName}
                />
                <InputField
                  control={form.control}
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder=""
                  type="date"
                  disabled={patientExists && patientData?.dateOfBirth}
                />
                <InputField
                  control={form.control}
                  name="phoneNumber"
                  label="Phone Number"
                  placeholder="Enter phone number"
                  type="tel"
                  disabled={patientExists && patientData?.phoneNumber}
                />
                  <CustomSelectForm
                  control={form.control}
                  name="gender"
                  label="Gender"
                  placeholder="Select gender"
                  options={[
                    { id: "Male", name: "Male" },
                    { id: "Female", name: "Female" },
                  ]}
                  disabled={patientExists && patientData?.gender}
                />
                <CustomSelectForm
                  control={form.control}
                  name="urgency"
                  label="Urgency"
                  placeholder="Select urgency level"
                  options={[
                    { id: "routine", name: "Routine" },
                    { id: "urgent", name: "Urgent" },
                    { id: "emergency", name: "Emergency" },
                  ]}
                />

                <InputField
                    control={form.control}
                    name="specialty"
                    label="Specialty"
                    placeholder="E.g Oncology"
                    type="specialty"
                  />

              </FormRow>
              <InputTextArea
                control={form.control}
                name="reason"
                label="Reason for Referral"
                placeholder="Enter reason for referral"
              />
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={onBack}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Submitting...' : 'Submit Referral'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
