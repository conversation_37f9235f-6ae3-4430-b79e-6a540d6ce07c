'use client';

import { CirclePercent } from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import DiscountRecord from './components/data-table';
import DiscountAndRefundRecord from './discount-and-refund/data-table';
import { Button } from '../ui/button';

export default function DiscountPage() {
  const canViewrReward = hasPermission(PERMISSIONS.REWARD_VIEW);
  const pathname = usePathname();
  const isDiscountAndRefund = pathname === '/discounts/discount-and-refund';
  
  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <CirclePercent className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Discount and Refund Management
      </h2>
      <div className="flex gap-2">
        {canViewrReward && (
          <>
            {isDiscountAndRefund ? (
              <Link href={'/discounts'}>
                <Button size='sm' className="cursor-pointer py-1.5 px-3">Package discount</Button>
              </Link>
            ) : (
                        <div className='flex gap-2'>
              <Link href={'/discounts/discount-and-refund'}>
                <Button size='sm' className="cursor-pointer py-1.5 px-3">Discount and Refund</Button>
              </Link>
                  <Link href={'/discounts/discount-code'}>
                    <Button variant='outline' size='sm' className="cursor-pointer">Discount Codes</Button>
                  </Link>
                  </div>
            )}
          </>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        {isDiscountAndRefund ? <DiscountAndRefundRecord /> : <DiscountRecord />}
      </div>
    </>
  );
}
