'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { BookOpen, Construction, Sparkles } from 'lucide-react';

export default function ProcessDictionaryContent() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <BookOpen className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Process Library
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive guide to all organizational processes.
          </p>
        </div>
      </div>

      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex justify-center sm:border border-gray-200 dark:border-[#1F1F23]">
        <div className="py-30 text-center">
          <h2 className="text-2xl font-bold py-2">No Process Found</h2>
          <p className="text-sm">
            Processes will become accessible once uploaded.
          </p>
        </div>
      </div>
    </div>
  );
}
