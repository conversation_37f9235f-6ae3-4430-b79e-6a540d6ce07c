'use client';

import React, { useState } from 'react';
import {
  InputField,
  FormRow,
  CurrencyInputField,
  FileUpload,
} from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  SupplyEntryFormSchema,
  SupplyEntry,
} from '@/components/validations/inventory';
import { ModalProps } from '@/components/types';

const SupplyEntryModal: React.FC<ModalProps> = ({
  setOpen,
  mutate,
  open,
  profile,
  props,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<SupplyEntry>({
    resolver: zodResolver(SupplyEntryFormSchema),
    defaultValues: {
      supplier: '',
      quantitySupplied: '',
      unitPrice: '',
      totalAmount: '',
      invoiceNo: '',
    },
  });

  const quantitySupplied = form.watch('quantitySupplied');
  const unitPrice = form.watch('unitPrice');

  React.useEffect(() => {
    const qty = Number(quantitySupplied) || 0;
    const price = Number(unitPrice) || 0;
    const total = qty * price;
    form.setValue('totalAmount', total.toString());
  }, [quantitySupplied, unitPrice, form]);

  const onSubmit = async (data: SupplyEntry) => {
    const file = data.invoice;

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('supplier', data.supplier);
      formData.append('quantitySupplied', data.quantitySupplied);
      formData.append('createdBy', profile.fullName);
      formData.append('costPerUnit', data.unitPrice);
      formData.append('inventoryItemId', props.id);
      formData.append('invoiceNo', data.invoiceNo);
      // formData.append('itemId', props.id);
      formData.append('invoiceImage', file);
      formData.append('originalName', file.name);

      const res = await myApi.post(
        '/cafeteria/inventory/new-supply',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (err) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add Supplies"
      description={`Enter supply details for ${props?.itemName || ''}`}
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FileUpload
            control={form.control}
            name="invoice"
            label="Invoice"
            accept="image/*,.pdf"
          />
          <FormRow>
            <InputField
              control={form.control}
              name="supplier"
              label="Supplier"
              placeholder="Enter supplier name"
              type="text"
            />
            <InputField
              control={form.control}
              name="quantitySupplied"
              label="Quantity Supplied"
              placeholder="Enter quantity"
              type="number"
            />
            <CurrencyInputField
              control={form.control}
              name="unitPrice"
              label="Unit Price"
              placeholder="Cost Price per unit"
            />
            <InputField
              control={form.control}
              name="invoiceNo"
              label="Invoice No"
              placeholder="Invoice number"
              type="text"
            />
          </FormRow>
          <CurrencyInputField
            control={form.control}
            name="totalAmount"
            label="Total Amount"
            placeholder="Total cost"
            disabled
          />
        </form>
      </Form>
    </Modal>
  );
};

export default SupplyEntryModal;
