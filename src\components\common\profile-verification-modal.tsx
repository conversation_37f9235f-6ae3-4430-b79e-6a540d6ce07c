'use client';

import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ProfileVerificationModalProps {
  open: boolean;
  isRetrying: boolean;
  onRetry: () => void;
  onLogout: () => void;
}

export function ProfileVerificationModal({
  open,
  isRetrying,
  onRetry,
  onLogout,
}: ProfileVerificationModalProps) {
  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent
        className="sm:max-w-md [&>button]:hidden"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
              <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <DialogTitle className="text-left">
                Profile Verification Failed
              </DialogTitle>
              <DialogDescription className="text-left">
                Unable to verify your profile. This may be a temporary issue.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            {isRetrying
              ? 'Retrying to verify your profile...'
              : 'Please try again or logout if the issue persists.'}
          </p>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onLogout}
            disabled={isRetrying}
            className="flex-1"
          >
            Logout
          </Button>
          <Button onClick={onRetry} disabled={isRetrying} className="flex-1">
            {isRetrying ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Retrying...
              </>
            ) : (
              'Retry'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
