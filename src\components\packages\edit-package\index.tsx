'use client';
import { PencilIcon, Banknote, InfoIcon } from 'lucide-react';
import { GetPackageAdminBySlug } from '@/api/data';
import PackageDetails from './components/package-details';
import PackagePriceEditor from './components/package-price-editor';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

interface Props {
  slug: string;
}

export default function EditPackage({ slug }: Props) {
  const { packageData, packageLoading, mutate } = GetPackageAdminBySlug(slug);
  const packagedata = packageData?.data;
  const [refreshKey, setRefreshKey] = useState(0);

  // Function to refresh data after updates
  const handleUpdateSuccess = () => {
    // Refresh the package data
    if (mutate) {
      mutate();
    }
    // Force re-render components
    setRefreshKey((prev) => prev + 1);
  };

  if (packageLoading) {
    return <div className="container mx-auto space-y-4">Loading...</div>;
  }

  return (
    <div className="container mx-auto space-y-4">
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <PencilIcon className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Edit Package - {packagedata.name}
        </h2>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <InfoIcon className="w-4 h-4" />
            Package Details
          </TabsTrigger>
          <TabsTrigger value="prices" className="flex items-center gap-2">
            <Banknote className="w-4 h-4" />
            Location Prices
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <PackageDetails
            key={`details-${refreshKey}`}
            packageData={packagedata}
            onUpdateSuccess={handleUpdateSuccess}
          />
        </TabsContent>

        <TabsContent value="prices">
          <PackagePriceEditor
            key={`prices-${refreshKey}`}
            packageId={packagedata.id}
            packageName={packagedata.name}
            initialPrices={packagedata.packageLocationPrices || []}
            onUpdateSuccess={handleUpdateSuccess}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
