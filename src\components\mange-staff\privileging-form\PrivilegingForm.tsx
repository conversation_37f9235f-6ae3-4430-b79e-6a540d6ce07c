'use client';

import { useEffect, useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FileText,
  ArrowLeft,
  Save,
  Send,
  CheckCircle,
  Download,
  Edit,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GetProfile } from '@/api/staff';
import StepPersonalInfo from './StepPersonalInfo';
import StepConsultancy from './StepConsultancy';
import StepPrivileges from './StepPrivileges';
import StepReferees from './StepReferees';
import StepAdditionalInfo from './StepAdditionalInfo';
import StepCover from './StepCover';
import StepIndemnity from './StepIndemnity';
import { toast } from 'sonner';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import ConfirmationModal from './ConfirmationModal';
import { myApi } from '@/api/fetcher';

const STORAGE_KEY = 'privileging-form-data';
const AUTO_SAVE_INTERVAL = 30000; // Auto-save every 30 seconds

const sections = [
  { title: 'Personal Information', component: StepPersonalInfo },
  { title: 'Category of Practising Privileges', component: StepPrivileges },
  { title: 'Consultancy Details', component: StepConsultancy },
  { title: 'Additional Information', component: StepAdditionalInfo },
  { title: 'Cover & Subcontracting', component: StepCover },
  { title: 'Referees', component: StepReferees },
  { title: 'Indemnity & Declarations', component: StepIndemnity },
];

export default function PrivilegingForm() {
  const [pdfGenerated, setPdfGenerated] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfSize, setPdfSize] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [formDataChanged, setFormDataChanged] = useState(false);
  const [signatureImageData, setSignatureImageData] = useState<string | null>(
    null
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const methods = useForm({
    defaultValues:
      typeof window !== 'undefined'
        ? JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}')
        : {},
  });

  const router = useRouter();
  const { profile: user } = GetProfile();
  const watchAllFields = methods.watch();

  // Track form changes to determine if PDF needs regeneration
  useEffect(() => {
    const subscription = methods.watch((value, { name }) => {
      if (pdfGenerated) {
        setFormDataChanged(true);
      }
      // Handle signature image upload - always update when new file is uploaded
      if (name === 'signature' && value.signature?.[0]) {
        const file = value.signature[0];
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageData = e.target?.result as string;
          setSignatureImageData(imageData);
          // Save immediately when image is updated
          setTimeout(() => saveFormData(), 100);
        };
        reader.readAsDataURL(file);
      }
    });
    return () => subscription.unsubscribe();
  }, [methods, pdfGenerated]);

  // Load saved form data
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        if (parsedData.signatureImageData) {
          setSignatureImageData(parsedData.signatureImageData);
          delete parsedData.signatureImageData;
        }
        Object.keys(parsedData).forEach((key) => {
          methods.setValue(key, parsedData[key]);
        });
        setLastSaved(new Date().toLocaleTimeString());
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, [methods]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      const formData = methods.getValues();
      localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
      setLastSaved(new Date().toLocaleTimeString());
    }, AUTO_SAVE_INTERVAL);

    return () => clearInterval(autoSaveInterval);
  }, [methods]);

  // Check if user is consultant
  useEffect(() => {
    if (user?.data && !user.data.doctorProfile?.isConsultant) {
      router.back();
    }
  }, [user, router]);

  const saveFormData = () => {
    const formData = methods.getValues();
    const dataToSave = {
      ...formData,
      signatureImageData: signatureImageData,
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    setLastSaved(new Date().toLocaleTimeString());
  };

  const handleSave = () => {
    setSaving(true);
    saveFormData();
    toast.success('Your progress has been saved. You can continue later.');
    setSaving(false);
  };

  const generatePDF = async (data: any) => {
    // Check if signature is required and available
    if (!signatureImageData && (!data.signature || !data.signature[0])) {
      toast.error('Please upload your signature before generating the PDF.');
      return;
    }

    try {
      toast.info('Generating PDF...');

      // Create a new PDF document with explicit unit specification
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      let yPosition = 20;
      const pageHeight = 297; // A4 height in mm
      const margin = 20;
      const lineHeight = 6;

      // Helper function to add new page if needed
      const checkPageBreak = (requiredSpace: number = 10) => {
        if (yPosition + requiredSpace > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
      };

      // Helper function to add text with word wrapping
      const addText = (
        text: string,
        x: number,
        fontSize: number = 10,
        maxWidth: number = 170
      ) => {
        doc.setFontSize(fontSize);
        const lines = doc.splitTextToSize(text, maxWidth);
        lines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, x, yPosition);
          yPosition += lineHeight;
        });
      };

      // Helper function to add section header
      const addSectionHeader = (title: string) => {
        checkPageBreak(15);
        yPosition += 5;
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text(title, margin, yPosition);
        yPosition += 8;
        doc.setFont('helvetica', 'normal');
      };

      // Helper function to add field
      const addField = (
        label: string,
        value: any,
        isTextArea: boolean = false
      ) => {
        const displayValue = String(value || 'Not provided');
        checkPageBreak(isTextArea ? 20 : 10);

        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        const labelLines = doc.splitTextToSize(`${label}:`, 170);
        labelLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin, yPosition);
          yPosition += lineHeight;
        });

        doc.setFont('helvetica', 'normal');
        const valueLines = doc.splitTextToSize(displayValue, 165);
        valueLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin + 5, yPosition);
          yPosition += lineHeight;
        });
        yPosition += 2; // Extra spacing
      };

      // Title
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      const titleLines = doc.splitTextToSize(
        'CEDARCREST HOSPITALS PRACTISING PRIVILEGES APPLICATION',
        170
      );
      titleLines.forEach((line: string) => {
        doc.text(line, 105, yPosition, { align: 'center' });
        yPosition += lineHeight;
      });
      yPosition += 4;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      const nameLines = doc.splitTextToSize(
        `NAME: ${user?.data.fullName || ''}`,
        170
      );
      nameLines.forEach((line: string) => {
        doc.text(line, 105, yPosition, { align: 'center' });
        yPosition += lineHeight;
      });
      yPosition += 2;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(
        `Generated on: ${new Date().toLocaleDateString()}`,
        105,
        yPosition,
        { align: 'center' }
      );
      yPosition += 8;

      // Personal Information Section
      addSectionHeader('PERSONAL INFORMATION');
      addField('Date of Birth', data.dob);
      addField('Nationality', data.nationality);
      addField('Ethnicity', data.ethnicity);
      addField('Gender', data.gender);

      // Home Details
      yPosition += 5;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      const homeDetailsLines = doc.splitTextToSize('Home Details', 170);
      homeDetailsLines.forEach((line: string) => {
        doc.text(line, margin, yPosition);
        yPosition += lineHeight;
      });
      yPosition += 2;
      doc.setFont('helvetica', 'normal');

      addField('Address', data.home?.address);
      addField('Telephone', data.home?.telephone);
      addField('Travel Time to Hospital', data.home?.travelTime);
      addField('Email Address', data.home?.email);

      // Next of Kin
      yPosition += 5;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      const nextOfKinLines = doc.splitTextToSize('Next of Kin', 170);
      nextOfKinLines.forEach((line: string) => {
        doc.text(line, margin, yPosition);
        yPosition += lineHeight;
      });
      yPosition += 2;
      doc.setFont('helvetica', 'normal');

      addField('Name', data.nextOfKin?.name);
      addField('Telephone', data.nextOfKin?.telephone);
      addField('Relationship', data.nextOfKin?.relationship);

      // Private Practice Address
      yPosition += 5;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      const practiceAddressLines = doc.splitTextToSize(
        'Private Practice Address',
        170
      );
      practiceAddressLines.forEach((line: string) => {
        doc.text(line, margin, yPosition);
        yPosition += lineHeight;
      });
      yPosition += 2;
      doc.setFont('helvetica', 'normal');

      addField('Address', data.practice?.address);
      addField('Telephone', data.practice?.telephone);
      addField('Email', data.practice?.email);
      addField('Website', data.practice?.website);

      // Privileges Section
      addSectionHeader('CATEGORY OF PRACTISING PRIVILEGES');

      const categories = [
        'Consultation in the out-patients department (including medico-legal work)',
        'Consultation and minor procedures in the out-patients department',
        'Consultation and minor procedures in the out-patients department plus admission of in/day-patients',
        'Consultation and minor procedures in the outpatient department plus admission and operative procedures (Declared Procedures) on in/day-patients',
        'Admission and care of in/day patients only',
        'Provision of anaesthetic services',
        'Diagnostic and therapeutic imaging procedures and reporting and provision of advice to colleagues including admission and interventional procedures',
        'Diagnostic and therapeutic imaging procedures, reporting of results and provision of advice to colleagues',
        'Pathology procedures and reporting and provision of advice to colleagues',
        'Administration of sedation by consultants other than Anaesthetists',
      ];

      categories.forEach((category, index) => {
        checkPageBreak(15);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        const categoryLines = doc.splitTextToSize(
          `${index + 1}. ${category}`,
          165
        );
        categoryLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin, yPosition);
          yPosition += lineHeight;
        });

        doc.setFont('helvetica', 'normal');
        const adults = data.privileges?.[index]?.adults ? 'Yes' : 'No';
        const paediatrics = data.privileges?.[index]?.paediatrics
          ? 'Yes'
          : 'No';
        const adultsLines = doc.splitTextToSize(`   Adults: ${adults}`, 165);
        adultsLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin + 5, yPosition);
          yPosition += lineHeight;
        });
        const paediatricsLines = doc.splitTextToSize(
          `   Paediatric: ${paediatrics}`,
          165
        );
        paediatricsLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin + 5, yPosition);
          yPosition += lineHeight;
        });
        yPosition += 2;
      });

      // Consultancy Details Section
      addSectionHeader('CONSULTANCY DETAILS');
      addField(
        'Date of Entry on Specialist Register',
        data.consultancy?.specialistRegisterDate
      );
      addField(
        'Current Government Hospital Consultant Post',
        data.consultancy?.currentGovtPost
      );
      if (data.consultancy?.currentGovtPost === 'Yes') {
        addField(
          'Current Government Hospital Name',
          data.consultancy?.currentGovtHospital
        );
        addField(
          'Clinical Sessions in Current Job Plan',
          data.consultancy?.clinicalSessions
        );
      }
      addField(
        'Previous Government Hospital Consultant Appointment',
        data.consultancy?.prevGovtHospital
      );
      addField(
        'Date of Leaving Previous Post',
        data.consultancy?.prevGovtLeaveDate
      );
      addField(
        'Reason for Leaving Previous Post',
        data.consultancy?.reasonForLeaving
      );
      addField(
        'Previous Locum Consultant Hospital',
        data.consultancy?.locumHospital
      );
      addField('Locum Consultant Dates', data.consultancy?.locumDates);
      addField('CV Gaps Explanation', data.consultancy?.gaps, true);
      addField(
        'Previous Work with Children/Vulnerable Adults',
        data.consultancy?.childrenWork,
        true
      );

      // Additional Information Section
      addSectionHeader('ADDITIONAL INFORMATION');
      addField(
        'Date of Last Medical Appraisal',
        data.additional?.lastAppraisal
      );
      addField(
        'Responsible Officer Details',
        data.additional?.responsibleOfficer
      );
      addField('CCST or CCT Awarded', data.additional?.ccst);
      if (data.additional?.ccst === 'Yes') {
        addField('CCST/CCT Award Date', data.additional?.ccstDate);
      }
      addField('Meets CME Requirements', data.additional?.cme);
      addField(
        'Hospitals Granted Practising Privileges',
        data.additional?.hospitalsGranted,
        true
      );
      addField(
        'Hospitals Refused Practising Privileges',
        data.additional?.hospitalsRefused,
        true
      );
      addField('Scope of Practice', data.additional?.scope, true);
      addField('Specialty', data.additional?.specialty);
      addField('Sub-Specialties', data.additional?.subSpecialty, true);
      addField('Training Breakdown', data.additional?.breakDown, true);
      addField(
        'Other Professional Organizations',
        data.additional?.organisations,
        true
      );

      // Cover & Subcontracting Section
      addSectionHeader('COVER & SUBCONTRACTING');
      addField('Support Structure for Safe Cover', data.cover?.support, true);
      addField(
        'Subcontracted Third-Party Arrangements',
        data.cover?.subcontract,
        true
      );
      addField('Tax Registration Number', data.cover?.taxNo);

      // Referees Section
      addSectionHeader('REFEREES');
      for (let i = 1; i <= 4; i++) {
        const referee = data.referees?.[i];
        if (
          referee?.name ||
          referee?.position ||
          referee?.telephone ||
          referee?.email
        ) {
          yPosition += 5;
          doc.setFontSize(12);
          doc.setFont('helvetica', 'bold');
          const refereeLines = doc.splitTextToSize(`Referee ${i}`, 170);
          refereeLines.forEach((line: string) => {
            doc.text(line, margin, yPosition);
            yPosition += lineHeight;
          });
          yPosition += 2;
          doc.setFont('helvetica', 'normal');

          addField('Full Name', referee?.name);
          addField('Position/Title', referee?.position);
          addField('Telephone', referee?.telephone);
          addField('Email', referee?.email);
          addField('Address', referee?.address);
          addField('Relationship to Applicant', referee?.relationship);
        }
      }

      // Medical Indemnity Section
      addSectionHeader('MEDICAL INDEMNITY');
      addField(
        'Member of Medical Defence Organisation',
        data.indemnity?.member
      );
      if (data.indemnity?.member === 'Yes') {
        addField('Organisation Name', data.indemnity?.organisation);
        addField(
          'Indemnity Reference/Certificate No',
          data.indemnity?.reference
        );
      }

      // Declarations Section
      addSectionHeader('DECLARATIONS');
      addField(
        'Financial Interests in Healthcare Companies',
        data.declarations?.financialInterests,
        true
      );
      addField(
        'Other Declarations/Conflicts of Interest',
        data.declarations?.otherDeclarations,
        true
      );
      addField('Police Investigation Status', data.declarations?.police, true);
      addField(
        'Criminal Conviction History',
        data.declarations?.conviction,
        true
      );
      addField(
        'Regulatory Investigation Status',
        data.declarations?.regulatory,
        true
      );
      addField(
        'Practice Conditions/Restrictions',
        data.declarations?.conditions,
        true
      );
      addField(
        'Professional Disqualification History',
        data.declarations?.disqualification,
        true
      );

      // Signature Section
      addSectionHeader('CONFIRMATION & SIGNATURE');
      doc.setFontSize(10);
      const confirmationText =
        'I hereby confirm that the details provided on this form are true and correct. I hereby give permission for Cedarcrest Healthcare Group to consult directly with the Medical and Dental Council of Nigeria (MDCN) to confirm any aspect of my medical indemnification on an ongoing basis until further notice.';
      const confirmationLines = doc.splitTextToSize(confirmationText, 165);
      confirmationLines.forEach((line: string) => {
        checkPageBreak();
        doc.text(line, margin, yPosition);
        yPosition += lineHeight;
      });
      yPosition += 5;

      // Handle signature image - prioritize stored image, fallback to file upload
      let imageProcessed = false;

      if (signatureImageData) {
        try {
          checkPageBreak(30);
          doc.text('Signature:', margin, yPosition);
          yPosition += lineHeight;
          doc.addImage(signatureImageData, 'JPEG', margin, yPosition, 50, 20);
          yPosition += 25;
          imageProcessed = true;
        } catch (error) {
          console.warn('Could not add stored signature image:', error);
        }
      }

      if (!imageProcessed && data.signature && data.signature[0]) {
        try {
          const file = data.signature[0];
          const reader = new FileReader();

          await new Promise((resolve) => {
            reader.onload = (e) => {
              try {
                const imgData = e.target?.result as string;
                setSignatureImageData(imgData);
                checkPageBreak(30);
                doc.text('Signature:', margin, yPosition);
                yPosition += lineHeight;
                doc.addImage(imgData, 'JPEG', margin, yPosition, 50, 20);
                yPosition += 25;
                imageProcessed = true;
                resolve(true);
              } catch (error) {
                console.warn('Could not add signature image:', error);
                resolve(true);
              }
            };
            reader.onerror = () => resolve(true);
            reader.readAsDataURL(file);
          });
        } catch (error) {
          console.warn('Error processing signature:', error);
        }
      }

      if (!imageProcessed) {
        throw new Error('Signature is required but could not be processed');
      }

      addField('Initials', data.shortSignature);
      // addField('Date', data.signatureDate);

      // Generate PDF blob
      const pdfBlob = doc.output('blob');

      // Create a URL for the blob
      const pdfUrl = URL.createObjectURL(pdfBlob);
      setPdfUrl(pdfUrl);

      // Calculate file size
      const fileSizeInKB = Math.round(pdfBlob.size / 1024);
      const fileSizeText = `${fileSizeInKB} KB`;
      setPdfSize(fileSizeText);

      setPdfGenerated(true);
      setFormDataChanged(false);
      toast.success(
        'Your application has been converted to PDF. You can now download or submit it.'
      );

      // Save the final form data
      saveFormData();
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      toast.error(`PDF generation failed: ${error.message || 'Unknown error'}`);
      setPdfGenerated(false);
      setPdfUrl(null);
      setPdfSize(null);
    }
  };

  const downloadPDF = () => {
    if (!pdfUrl) {
      toast.error('PDF not available. Please generate the PDF first.');
      return;
    }

    toast.success('Your PDF is being downloaded...');

    try {
      // Create a download link
      const link = document.createElement('a');
      link.href = pdfUrl;
      const fileName = user?.data?.fullName
        ? `${user.data.fullName.replace(/[^a-zA-Z0-9]/g, '_')}_Privileging_Application.pdf`
        : `privileging-application-${Date.now()}.pdf`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Your PDF has been downloaded successfully.');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Failed to download PDF. Please try again.');
    }
  };

  const handleSubmitClick = () => {
    setShowConfirmModal(true);
  };

  const submitApplication = async () => {
    if (!pdfUrl || !user?.data?.id) {
      toast.error('Missing required data for submission.');
      return;
    }

    try {
      setIsSubmitting(true);

      // Convert PDF URL to blob and create file
      const response = await fetch(pdfUrl);
      const pdfBlob = await response.blob();
      const fileName = user.data.fullName
        ? `${user.data.fullName.replace(/[^a-zA-Z0-9]/g, '_')}_Privileging_Application.pdf`
        : `privileging-application-${Date.now()}.pdf`;

      const formData = new FormData();
      formData.append('itemId', user.data.id);
      formData.append('doctorPrivileging', pdfBlob, fileName);
      formData.append('originalName', fileName);

      const res = await myApi.post('/staff/doctor-privileging', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setIsSubmitting(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setShowConfirmModal(false);
        router.push('/dashboard');
      }
    } catch (err) {
      setIsSubmitting(false);
      console.error('Submission error:', err);
    }
  };

  if (!user?.data?.doctorProfile?.isConsultant) {
    return null;
  }

  // Check if privileging document already exists
  const hasSubmittedDocument =
    user?.data?.doctorProfile?.privilegingDocumentUrl;
  const privilegingStatus =
    user?.data?.doctorProfile?.privilegingDocumentStatus;
  const privilegingRemarks =
    user?.data?.doctorProfile?.privilegingDocumentRemarks;
  const isApproved = privilegingStatus === 'APPROVED';

  // Format status for user-friendly display
  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'SUBMITTED':
        return 'Your application has been submitted successfully';
      case 'UNDER_REVIEW':
        return 'Your submitted application is currently being reviewed';
      case 'APPROVED':
        return 'Your application has been approved';
      case 'REJECTED':
        return 'Your application has been rejected and requires resubmission';
      default:
        return 'Your application has been submitted';
    }
  };

  // If document exists and is approved, show success message
  if (hasSubmittedDocument && isApproved) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => window.history.back()}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </div>
          <Card>
            <CardContent className="p-8 text-center">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-foreground mb-2">
                Privileging Application Approved
              </h2>
              <p className="text-muted-foreground mb-6">
                Your privileging application has been approved. No further
                action is required.
              </p>
              <Button
                onClick={() =>
                  window.open(
                    `${process.env.NEXT_PUBLIC_CEDARCREST_API_BASE}/${hasSubmittedDocument}`,
                    '_blank'
                  )
                }
                variant="outline"
              >
                <FileText className="w-4 h-4 mr-2" />
                View Approved Document
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // If document exists but not approved, show preview with refill option
  if (hasSubmittedDocument && !showForm) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => window.history.back()}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <h1 className="text-3xl font-bold text-foreground">
              Privileging Application Status
            </h1>
            <p className="text-muted-foreground mt-2">
              {getStatusMessage(privilegingStatus || 'SUBMITTED')}
            </p>
          </div>
          {privilegingRemarks && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                Feedback/Comments:
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {privilegingRemarks}
              </p>
            </div>
          )}
          <Card>
            <CardContent className="p-6">
              <div className="text-center mb-6">
                <FileText className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                <h2 className="text-xl font-bold text-foreground mb-2">
                  Application Submitted
                </h2>
                <p className="text-muted-foreground mb-4">
                  {getStatusMessage(privilegingStatus || 'SUBMITTED')}
                </p>
              </div>
              <div className="flex justify-center gap-4">
                <Button
                  onClick={() =>
                    window.open(
                      `${process.env.NEXT_PUBLIC_CEDARCREST_API_BASE}/${hasSubmittedDocument}`,
                      '_blank'
                    )
                  }
                  variant="outline"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  View Submitted Document
                </Button>
                <Button
                  onClick={() => {
                    if (privilegingStatus === 'REJECTED') {
                      localStorage.removeItem(STORAGE_KEY);
                    }
                    setShowForm(true);
                  }}
                  variant="default"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  {privilegingStatus === 'REJECTED'
                    ? 'Refill Application'
                    : 'Edit Application'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <Button
              variant="ghost"
              onClick={() =>
                hasSubmittedDocument
                  ? setShowForm(false)
                  : window.history.back()
              }
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              {hasSubmittedDocument ? 'Back to Status' : 'Back'}
            </Button>
            {hasSubmittedDocument && (
              <Button variant="outline" onClick={() => setShowForm(false)}>
                View Status
              </Button>
            )}
          </div>
          <h1 className="text-3xl font-bold text-foreground">
            Privileging Application Form
          </h1>
          <p className="text-muted-foreground mt-2">
            Complete your privileging application
            {lastSaved && (
              <span className="ml-2 text-sm text-muted-foreground">
                (Last saved: {lastSaved})
              </span>
            )}
          </p>
        </div>
        {privilegingRemarks && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              Feedback/Comments:
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {privilegingRemarks}
            </p>
          </div>
        )}

        <FormProvider {...methods}>
          <form className="space-y-8">
            {/* Form Sections */}
            {sections.map((section, index) => {
              const SectionComponent = section.component;
              return (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle>{section.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <SectionComponent />
                  </CardContent>
                </Card>
              );
            })}

            {/* Action Buttons */}
            <div className="flex flex-wrap justify-between gap-2 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>Saving...</>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Progress
                  </>
                )}
              </Button>

              <div className="flex flex-wrap gap-2 space-x-4">
                <Button
                  type="button"
                  size="lg"
                  onClick={() => generatePDF(methods.getValues())}
                  variant={formDataChanged ? 'default' : 'outline'}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  {pdfGenerated && formDataChanged
                    ? 'Regenerate PDF'
                    : 'Convert to PDF'}
                </Button>

                {pdfGenerated && (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={downloadPDF}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download PDF {pdfSize && `(${pdfSize})`}
                    </Button>
                    <Button
                      type="button"
                      onClick={handleSubmitClick}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={formDataChanged || isSubmitting}
                    >
                      <Send className="w-4 h-4 mr-2" />
                      {isSubmitting ? 'Submitting...' : 'Submit Application'}
                    </Button>
                  </>
                )}
              </div>
            </div>

            {pdfGenerated && (
              <div
                className={`p-4 rounded-lg border flex items-center ${
                  formDataChanged
                    ? 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800'
                    : 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                }`}
              >
                <CheckCircle
                  className={`w-5 h-5 mr-2 ${
                    formDataChanged
                      ? 'text-amber-600 dark:text-amber-400'
                      : 'text-green-600 dark:text-green-400'
                  }`}
                />
                <p
                  className={
                    formDataChanged
                      ? 'text-amber-800 dark:text-amber-300'
                      : 'text-green-800 dark:text-green-300'
                  }
                >
                  {formDataChanged
                    ? 'Form data has changed since PDF generation. Please regenerate the PDF to reflect your latest changes before submitting.'
                    : 'PDF has been generated successfully. You can now download or submit your application.'}
                </p>
              </div>
            )}

            <ConfirmationModal
              isOpen={showConfirmModal}
              onClose={() => setShowConfirmModal(false)}
              onConfirm={submitApplication}
              title="Submit Application"
              description="Are you sure you want to submit your privileging application? This action cannot be undone and your application will be sent for review."
              confirmText="Submit Application"
              cancelText="Cancel"
            />
          </form>
        </FormProvider>
      </div>
    </div>
  );
}
