import React, { useState } from 'react';
import {
  InputField,
  CurrencyInputField,
  FormRow,
  InputCalendar,
} from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Check } from 'lucide-react';
import {
  discountPriceFormSchema,
  DiscountForm,
} from '@/components/validations/locationPrice';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import { MultiSelect } from '@/components/common/multi-select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { GetPackagePrices } from '@/api/data';

interface Price {
  id: string;
  name: string;
}

const today = new Date();
today.setHours(0, 0, 0, 0);

const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const { packagePrices } = GetPackagePrices();
  const [isLoading, setIsLoading] = useState(false);
  const [isPercentageMode, setIsPercentageMode] = useState(true);
  const [selectedPrices, setSelectedPrices] = useState<string[]>([]);

  const data = packagePrices?.data;

  const handleToggle = (checked: boolean) => {
    setIsPercentageMode(checked);
    if (checked) {
      form.setValue('amount', '');
    } else {
      form.setValue('percentage', '');
    }
  };

  const form = useForm<DiscountForm>({
    resolver: zodResolver(discountPriceFormSchema),
    defaultValues: {
      code: '',
      amount: '',
      percentage: '',
      description: '',
    },
  });

  const onSubmit = async (data: DiscountForm) => {
    if (data.amount === '' && data.percentage === '') {
      toast.error('Please input either amount or percentage');
      return;
    }
    try {
      setIsLoading(true);
      const res = await myApi.post('/discount/new-discount', {
        code: data.code,
        description: data.description,
        startDate: data.startDate.toISOString(),
        endDate: data.endDate.toISOString(),
        packages: selectedPrices,
        amount: data.amount ? parseFloat(data.amount).toFixed(2) : undefined,
        percentage: data.percentage ? parseFloat(data.percentage) : undefined,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Create Discount"
      description="Enter a new discount code and attach it to packages"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-8">
          <div className="flex items-center gap-4">
            <Label htmlFor="discountMode">Use Percentage</Label>
            <Switch
              id="discountMode"
              checked={isPercentageMode}
              onCheckedChange={handleToggle}
            />
          </div>
          <FormRow>
            {isPercentageMode ? (
              <InputField
                control={form.control}
                name="percentage"
                label="Percentage"
                placeholder="%"
                type="text"
              />
            ) : (
              <CurrencyInputField
                control={form.control}
                name="amount"
                label="Amount"
                placeholder="Enter amount"
              />
            )}
            <InputField
              control={form.control}
              name="code"
              label="Discount Code"
              placeholder="Enter discount code"
              type="text"
            />
            <InputCalendar
              control={form.control}
              name="startDate"
              label="Start Date"
              minDate={today}
            />
            <InputCalendar
              control={form.control}
              name="endDate"
              label="End Date"
              minDate={new Date()}
            />
          </FormRow>
          <InputField
            control={form.control}
            name="description"
            label="Description"
            placeholder="E.g Festive period discount"
            type="text"
          />
          <div className="space-y-2">
            <Label htmlFor="prices">Select package prices</Label>
            <MultiSelect
              className="overflow-auto"
              options={data ?? []}
              selected={selectedPrices}
              onChange={setSelectedPrices}
              placeholder="Select package prices"
              valueField="id"
              labelField="name"
              badgeClassName="bg-primary text-primary-foreground hover:bg-primary/90"
              renderOption={(price, isSelected) => (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="mr-2 flex h-4 w-4 items-center justify-center">
                      {isSelected ? <Check className="h-4 w-4" /> : null}
                    </div>
                    <span>{(price as unknown as Price).name}</span>
                  </div>
                </div>
              )}
            />
            {selectedPrices.length === 0 && form.formState.isSubmitted && (
              <p className="text-sm text-red-500 mt-1">
                At least one package price must be selected
              </p>
            )}
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
