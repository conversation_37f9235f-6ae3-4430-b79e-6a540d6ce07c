export const SOCKET_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',

  // Notification events
  NOTIFICATION: 'new_notification',

  // User presence events
  USER_PRESENCE: 'user_online',
  UPDATE_PRESENCE: 'status_update',

  // Booking events
  PACKAGE_BOOKING: 'new_package_booking',
  BOOKING_UPDATE: 'booking_update',

  // Cafeteria events
  ORDER_CREATED: 'order_created',
  ORDER_UPDATE: 'order_update',
  SPECIAL_ORDER_UPDATE: 'special_order_update',

  // Transaction events
  TRANSACTION_UPDATE: 'transaction_update',
  WALLET_UPDATE: 'wallet_update',

  // Reward events
  REWARD_RECEIVED: 'reward_received',

  // System events
  SYSTEM_NOTIFICATION: 'system_notification',
  MAINTENANCE_NOTIFICATION: 'maintenance_notification',
  EMERGENCY_NOTIFICATION: 'emergency_notification',
  NOTIFICATION_SYNC: 'notification_sync',

  // Referral events
  NEW_REFERRAL_CREATED: 'new_referral_created',
  REFERRAL_STATUS_UPDATED: 'referral_status_updated',
  REFERRAL_COMMENT_ADDED: 'referral_comment_added',

  // Staff events
  NEW_STAFF_CREATED: 'new_staff_created',
  PROFILE_UPDATED: 'profile_updated',
  LOGIN_SUCCESSFUL: 'login_successful',

  // Presence events
  USER_OFFLINE: 'user_offline',
  USER_STATUS_CHANGED: 'user_status_changed',
} as const;

export const SOCKET_CONFIG = {
  transports: ['websocket', 'polling'] as string[],
  autoConnect: true,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  timeout: 20000,
} as const;
