'use client';

import React from 'react';
import { Lightbulb, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface EmptyStateProps {
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = 'No ideas yet',
  description = 'Be the first to share your innovative idea and inspire others!',
  actionText = 'Share Your Idea',
  onAction,
}) => {
  return (
    <Card className="border-dashed border-2 border-gray-300 dark:border-gray-600">
      <CardHeader className="text-center pb-4">
        <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <Lightbulb className="w-8 h-8 text-gray-400 dark:text-gray-500" />
        </div>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {description}
        </p>
        {onAction && (
          <Button onClick={onAction} className="mt-4">
            <Plus className="w-4 h-4 mr-2" />
            {actionText}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
