import { z } from 'zod';

const twoDaysFromNow = new Date();
twoDaysFromNow.setDate(twoDaysFromNow.getDate() + 2);

export const locationFormSchema = z.object({
  name: z.string().min(1, { message: 'Location name is required' }),
  region: z.string().min(1, { message: 'This is required' }),
  country: z.string().min(1, { message: 'This is required' }),
  currency: z.string().optional(),
  longitude: z
    .string()
    .optional()
    .refine((val) => !val || !isNaN(parseFloat(val)), {
      message: 'Longitude must be a valid number.',
    })
    .transform((val) => (val ? parseFloat(val) : undefined)),
  latitude: z
    .string()
    .optional()
    .refine((val) => !val || !isNaN(parseFloat(val)), {
      message: 'Longitude must be a valid number.',
    })
    .transform((val) => (val ? parseFloat(val) : undefined)),
});

export const discountPriceFormSchema = z.object({
  code: z.string().min(2, { message: 'Discount code is required' }),
  startDate: z.date().refine(
    (val) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to today's midnight
      return val >= today;
    },
    {
      message: 'Start date cannot be in the past',
    }
  ),
  endDate: z.date().refine((val) => val > new Date(), {
    message: 'End date must be a later date',
  }),
  amount: z
    .string()
    .optional()
    .refine((val) => !val || /^\d*\.?\d+$/.test(val), {
      message: 'Amount must be a valid number',
    }),
  percentage: z
    .string()
    .optional()
    .refine((val) => !val || /^\d*\.?\d+$/.test(val), {
      message: 'Percentage must be a valid number',
    }),
  description: z.string().min(10, { message: 'Enter description' }),
});

export type Location = z.infer<typeof locationFormSchema>;
export type DiscountForm = z.infer<typeof discountPriceFormSchema>;
