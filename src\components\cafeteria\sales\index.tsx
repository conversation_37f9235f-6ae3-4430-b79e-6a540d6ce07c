'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Minus,
  ShoppingCart,
  Trash2,
  Users,
  UserRound,
  Search,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PaymentModal from './PaymentModal';
import { GetAllMenu, GetAllMenuCat } from '@/api/cafeteria/menu';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import { currencyFormat } from '@/lib/utils';
import Pagination from '@/components/common/pagination';

type MenuItem = {
  id: number;
  name: string;
  generalPrice: number;
  staffPrice: number;
  menuCategory: {
    id: number;
    name: string;
  };
  isAvailable: boolean;
};

type CartItem = {
  id: number;
  name: string;
  price: number;
  quantity: number;
};

export default function SalesManagement() {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >(null);
  const [saleType, setSaleType] = useState<'general' | 'staff'>('staff');

  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
  } = useSearchAndPagination({ initialPageSize: 12 });

  const { menu, menuLoading, mutate } = GetAllMenu(
    `?page=${currentPage}&limit=${pageSize}&available=true${activeCategory ? `&category=${activeCategory}` : ''}${queryParam ? `&${queryParam}` : ''}`
  );
  const { menuCat } = GetAllMenuCat();
  const menuData: MenuItem[] = menu?.data?.menu || [];
  const totalPages = menu?.data?.totalPages ?? 0;
  const categories = menuCat?.data || [];

  // Filter menu items based on search term (category filtering is done by API)
  const filteredMenuItems = menuData.filter((item: MenuItem) => {
    const matchesSearch = item.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesSearch && item.isAvailable;
  });

  // Add item to cart with correct price based on sale type
  const addToCart = (menuItem: MenuItem) => {
    const price =
      saleType === 'general' ? menuItem.generalPrice : menuItem.staffPrice;
    const item = {
      id: menuItem.id,
      name: menuItem.name,
      price: price,
    };

    setCart((prevCart) => {
      const existingItem = prevCart.find((cartItem) => cartItem.id === item.id);
      if (existingItem) {
        return prevCart.map((cartItem) =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prevCart, { ...item, quantity: 1 }];
      }
    });
  };

  // Update item quantity in cart
  const updateQuantity = (id: number, change: number) => {
    setCart((prevCart) => {
      return prevCart
        .map((item) => {
          if (item.id === id) {
            const newQuantity = Math.max(0, item.quantity + change);
            return { ...item, quantity: newQuantity };
          }
          return item;
        })
        .filter((item) => item.quantity > 0);
    });
  };

  // Calculate total price
  const totalPrice = cart.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  // Handle payment success callback
  const handlePaymentSuccess = () => {
    setCart([]);
    setPaymentModalOpen(false);
    setSelectedPaymentMethod(null);
  };

  return (
    <>
      <div className="flex justify-center mb-4">
        <Tabs
          value={saleType}
          onValueChange={(value) => setSaleType(value as 'general' | 'staff')}
          className="w-1/2"
        >
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger
              value="general"
              className="flex items-center gap-2"
              disabled={cart.length > 0}
            >
              <Users className="w-4 h-4" />
              General
            </TabsTrigger>
            <TabsTrigger
              value="staff"
              className="flex items-center gap-2"
              disabled={cart.length > 0}
            >
              <UserRound className="w-4 h-4" />
              Staff
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Menu Items Section */}
        <div className="md:col-span-2 space-y-4">
          <div className="flex flex-col space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search ..."
                className="pl-8 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                variant={activeCategory === null ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setActiveCategory(null);
                  handlePageChange(1);
                }}
              >
                All
              </Button>
              {categories.map((category: any) => (
                <Button
                  key={category.id}
                  variant={
                    activeCategory === category.name ? 'default' : 'outline'
                  }
                  size="sm"
                  onClick={() => {
                    setActiveCategory(category.name);
                    handlePageChange(1);
                  }}
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {menuLoading ? (
              <div className="col-span-3 text-center py-8 text-gray-500">
                Loading menu items...
              </div>
            ) : filteredMenuItems.length === 0 ? (
              <div className="col-span-3 text-center py-8 text-gray-500">
                No menu items available
              </div>
            ) : (
              filteredMenuItems.map((item: MenuItem) => {
                const displayPrice =
                  saleType === 'general' ? item.generalPrice : item.staffPrice;
                return (
                  <Card
                    key={item.id}
                    className="overflow-hidden border-gray-400 cursor-pointer hover:shadow-md dark:hover:bg-gray-800 hover:bg-gray-100 transition-shadow"
                    onClick={() => addToCart(item)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <Badge variant="outline" className="mt-1">
                            {item.menuCategory.name}
                          </Badge>
                        </div>
                        <span className="font-bold">
                          {currencyFormat(displayPrice)}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
          {totalPages > 1 ? (
            <Pagination
              totalPages={totalPages}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          ) : (
            ''
          )}
        </div>

        {/* Cart Section */}
        <div className="md:col-span-1">
          <Card className="sticky top-4">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  Cart
                </h3>
                {cart.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCart([])}
                  >
                    Clear All
                  </Button>
                )}
              </div>

              {cart.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Your cart is empty
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map((item) => (
                    <div
                      key={item.id}
                      className="flex justify-between items-center py-2 border-b"
                    >
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">
                          {currencyFormat(item.price)} × {item.quantity}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, -1)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-6 text-center">{item.quantity}</span>
                        <Button
                          size="icon"
                          variant="outline"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8 text-red-500"
                          onClick={() =>
                            updateQuantity(item.id, -item.quantity)
                          }
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  <div className="pt-4 border-t">
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>{currencyFormat(totalPrice)}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <div className="p-4 border-t">
              <Button
                className="w-full"
                disabled={cart.length === 0}
                onClick={() => setPaymentModalOpen(true)}
              >
                Complete Sale
              </Button>
            </div>

            {/* Payment Modal */}
            <PaymentModal
              open={paymentModalOpen}
              onOpenChange={setPaymentModalOpen}
              totalPrice={totalPrice}
              saleType={saleType}
              selectedPaymentMethod={selectedPaymentMethod}
              setSelectedPaymentMethod={setSelectedPaymentMethod}
              cart={cart}
              onPaymentSuccess={handlePaymentSuccess}
            />
          </Card>
        </div>
      </div>
    </>
  );
}
