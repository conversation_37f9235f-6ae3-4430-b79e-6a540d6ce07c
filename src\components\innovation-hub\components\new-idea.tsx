import React, { useState } from 'react';
import { InputField, InputTextArea, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { CustomSelectForm } from '@/components/common/another';
import { ModalProps } from '@/components/types';
import { MultiSelect } from '@/components/common/multi-select';
import { GetIdeaCategories, GetIdeaTags } from '@/api/innovation-hub/data';
import { IdeaFormValues, ideaSchema } from '@/components/validations/idea';

const NewIdea: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const { ideasCat } = GetIdeaCategories();
  const { ideasTags } = GetIdeaTags();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<IdeaFormValues>({
    resolver: zodResolver(ideaSchema),
    defaultValues: {
      title: '',
      description: '',
      tagIds: [],
    },
  });

  const onSubmit = async (data: IdeaFormValues) => {
    console.log(data);
    try {
      setIsLoading(true);
      const res = await myApi.post('/innovation/new-idea', data);
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Share Your Idea"
      description=" Contribute to our innovation pipeline. What's your next big idea?"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-6">
          <FormRow>
            <CustomSelectForm
              control={form.control}
              name="categoryId"
              label="Category"
              placeholder="Select a suitable category"
              options={ideasCat}
            />
            <InputField
              control={form.control}
              name="title"
              label="Idea Title"
              placeholder="e.g., AI-Powered Analytics"
            />
          </FormRow>
          <InputTextArea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Describe your idea in detail..."
          />
          <div className="space-y-2">
            <label className="text-sm font-medium">Tags</label>
            <MultiSelect
              options={ideasTags}
              selected={form.watch('tagIds') || []}
              onChange={(value) => form.setValue('tagIds', value)}
              placeholder="Select as many tags that best represent the idea..."
              valueField="id"
              labelField="name"
            />
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default NewIdea;
