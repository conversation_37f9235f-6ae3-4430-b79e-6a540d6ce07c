import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Mailbox } from 'lucide-react';
import { myApi } from '@/api/fetcher';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

// Idea Card Component
export const SuggestionCard = ({
  data,
  canEdit,
  mutate,
}: {
  data: any;
  canEdit: boolean;
  mutate: any;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [loadingMarkRead, setLoadingMarkRead] = useState(false);
  const [comment, setComment] = useState('');
  const maxLength = 150;
  const shouldTruncate = data.content.length > maxLength;
  const displayContent =
    isExpanded || !shouldTruncate
      ? data.content
      : data.content.substring(0, maxLength) + '...';

  const handleCloseComment = () => {
    setShowCommentInput(false);
    setComment('');
  };

  const onMarkRead = async (id: any) => {
    try {
      setLoadingMarkRead(true);
      const payload: any = { suggestionId: Number(id) };
      if (comment.trim()) {
        payload.comment = comment;
      }
      const res = await myApi.patch('/suggestions/update', payload);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        handleCloseComment();
        if (mutate) mutate();
      }
    } catch (error) {
      console.error('Error updating suggestion', error);
    } finally {
      setLoadingMarkRead(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback className="dark:bg-green-100/10 bg-green-100">
                <Mailbox className="h-5 w-5 text-green-700" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-1">
                <p className="font-semibold text-xs">
                  {data.anonymous ? 'Anonymous' : data.staff.fullName}
                </p>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {dayjs(data.createdAt).fromNow()} |{' '}
                <span
                  className={
                    data.isRead ? 'text-green-500' : 'text-destructive'
                  }
                >
                  {data.isRead ? 'Read' : 'Not Read'}
                </span>
              </p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription>
          {displayContent}
          {shouldTruncate && (
            <button
              onClick={() => {
                setIsExpanded(!isExpanded);
              }}
              className="ml-2 cursor-pointer text-red-900 hover:text-red-800"
            >
              {isExpanded ? 'Show less' : 'Show more'}
            </button>
          )}
          {data.comment && (
            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
              <p className="font-semibold text-xs text-gray-600 dark:text-gray-400">
                <span>Comment:</span> {data.comment}
              </p>
            </div>
          )}
        </CardDescription>
      </CardContent>
      {canEdit && (
        <CardFooter className="flex flex-col space-y-3">
          <div className="flex justify-between items-center w-full">
            <div className="flex space-x-2 text-gray-600 dark:text-gray-400">
              {!data.isRead && !showCommentInput && (
                <Button
                  disabled={data.isRead}
                  size="sm"
                  className="text-xs"
                  onClick={() => setShowCommentInput(true)}
                >
                  Mark Read
                </Button>
              )}
            </div>
          </div>
          {showCommentInput && (
            <div className="w-full space-y-2">
              <Textarea
                placeholder="Add a comment (optional)"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
              />
              <div className="flex space-x-2">
                <Button
                  disabled={loadingMarkRead}
                  size="sm"
                  onClick={() => {
                    onMarkRead(data.id);
                  }}
                >
                  {loadingMarkRead ? 'Please wait..' : 'Confirm'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  disabled={loadingMarkRead}
                  onClick={() => {
                    handleCloseComment();
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardFooter>
      )}
    </Card>
  );
};
