import React, { useState, useRef, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Edit,
  MessageCircle,
  MoreHorizontal,
  ThumbsUp,
  Trash2,
  Trophy,
  Share2,
  Check,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Lightbulb } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Modal } from '@/components/common/modal';
import { ideaStatuses } from '../components/types';
import { Idea, IdeaStatus } from '@/api/innovation-hub/data';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

// Status Badge Component
const StatusBadge = ({ status }: { status: IdeaStatus }) => {
  const statusStyles: Record<IdeaStatus, string> = {
    DRAFT: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    PENDING_REVIEW:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    ACCEPTED:
      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    REJECTED: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    IMPLEMENTED:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  };

  const formatStatus = (status: string) => {
    return status
      .toLowerCase()
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  return (
    <Badge className={`${statusStyles[status]} border-none`}>
      {formatStatus(status)}
    </Badge>
  );
};

// Tags Component
const TagsDisplay = ({ tags }: { tags: any[] }) => {
  const [visibleTags, setVisibleTags] = useState<any[]>([]);
  const [remainingCount, setRemainingCount] = useState(0);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    if (!tags?.length) return;
    const maxVisible = isSmallScreen ? 1 : 3;
    setVisibleTags(tags.slice(0, maxVisible));
    setRemainingCount(Math.max(0, tags.length - maxVisible));
  }, [tags, isSmallScreen]);

  if (!tags?.length) return null;

  return (
    <div className="flex flex-wrap gap-1 mt-2" ref={containerRef}>
      {visibleTags.map((tag) => (
        <Badge key={tag.id} variant="outline" className="text-xs">
          {tag.name}
        </Badge>
      ))}
      {remainingCount > 0 && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="text-xs cursor-pointer">
                +{remainingCount}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="flex flex-wrap gap-1 max-w-xs">
                {tags.slice(isSmallScreen ? 1 : 3).map((tag) => (
                  <Badge key={tag.id} variant="outline" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};

// Idea Card Component
export const IdeaCard = ({
  idea,
  onLike,
  canEdit,
  onDelete,
  onStatusUpdate,
  onCommentClick,
  isChampion = false,
}: {
  idea: Idea & { tags?: any[] };
  onLike: (id: number) => void;
  canEdit: boolean;
  onDelete: (id: number) => void;
  onStatusUpdate: (id: number, status: IdeaStatus) => void;
  onCommentClick: (idea: Idea) => void;
  isChampion?: boolean;
}) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [copied, setCopied] = useState(false);
  const searchParams = useSearchParams();

  const handleShare = async () => {
    const url = new URL(window.location.href);
    const slug = idea.slug;
    url.searchParams.set('slug', slug);

    try {
      await navigator.clipboard.writeText(url.toString());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarFallback className="dark:bg-green-100/10 bg-green-100">
                <Lightbulb className="h-5 w-5 text-green-700" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex flex-wrap items-center gap-1">
                <p className="font-semibold text-xs">
                  {idea.author.fullName}
                  {isChampion && (
                    <span className="text-gray-500">
                      {' '}
                      ({idea.rewardPoints} points)
                    </span>
                  )}
                </p>
                {isChampion && (
                  <div className="flex items-center gap-1">
                    <Trophy className="h-3 w-3 text-yellow-500" />
                    <span className="text-xs font-medium text-yellow-600 whitespace-nowrap">
                      Innovation Champion
                    </span>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {dayjs(idea.createdAt).fromNow()}
              </p>
            </div>
          </div>
          {canEdit && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Update Status</span>
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      {(() => {
                        const getValidStatuses = (
                          currentStatus: IdeaStatus
                        ): IdeaStatus[] => {
                          switch (currentStatus) {
                            case 'DRAFT':
                              return ['PENDING_REVIEW'];
                            case 'PENDING_REVIEW':
                              return ['ACCEPTED', 'REJECTED'];
                            case 'ACCEPTED':
                              return ['IMPLEMENTED'];
                            case 'REJECTED':
                            case 'IMPLEMENTED':
                            default:
                              return [];
                          }
                        };

                        return getValidStatuses(idea.status).map((status) => (
                          <DropdownMenuItem
                            key={status}
                            onClick={() => onStatusUpdate(idea.id, status)}
                          >
                            {status.replace(/_/g, ' ')}
                          </DropdownMenuItem>
                        ));
                      })()}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-red-500 focus:text-red-500"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap-reverse items-center justify-between mb-2">
          <CardTitle className="sm:text-lg font-semibold text-primary">
            {idea.title}
          </CardTitle>
          <StatusBadge status={idea.status} />
        </div>
        <CardDescription className="text-xs sm:text-sm">
          {idea.description}
        </CardDescription>
        <TagsDisplay tags={idea.tags || []} />
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        <div className="flex space-x-4 text-gray-600 dark:text-gray-400">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onLike(idea.id)}
                >
                  {idea?.likes?.length} <ThumbsUp className="h-4 w-4 ml-1" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {idea?.likes?.length ? (
                  <div className="max-w-xs">
                    <p className="font-semibold mb-1">Liked by:</p>
                    {idea?.likes?.map((user: any, index: number) => (
                      <span className="capitalize" key={user.id}>
                        {user?.staff?.fullName || 'Unknown'}
                        {index < idea.likes!.length - 1 ? ', ' : ''}
                      </span>
                    ))}
                  </div>
                ) : (
                  'No likes yet'
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCommentClick(idea)}
          >
            {idea.comments.length} <MessageCircle className="h-4 w-4 ml-1" />
          </Button>
        </div>
        <Button variant="ghost" size="sm" onClick={handleShare}>
          {copied ? (
            <Check className="h-4 w-4 mr-2" />
          ) : (
            <Share2 className="h-4 w-4 mr-2" />
          )}
          {copied ? 'Copied!' : 'Share'}
        </Button>
      </CardFooter>

      <Modal
        open={showDeleteDialog}
        setOpen={setShowDeleteDialog}
        title="Delete Idea"
        description={`Are you sure you want to delete "${idea.title}"? This action cannot be undone.`}
      >
        <div className="flex gap-2 justify-end">
          <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={() => {
              onDelete(idea.id);
              setShowDeleteDialog(false);
            }}
          >
            Delete
          </Button>
        </div>
      </Modal>
    </Card>
  );
};
