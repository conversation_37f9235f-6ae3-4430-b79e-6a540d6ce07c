'use client';

import React, { useState } from 'react';
import { Plus, Group } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Create from './components/create';
import Category from './components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function PackageCategory() {
  const [open, setOpen] = useState(false);
  const permitEdit = hasPermission(PERMISSIONS.PACKAGE_CREATE);

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <Group className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Manage Category
        </h2>
        <div>
          <div className="flex flex-wrap gap-2">
            {hasPermission(PERMISSIONS.PACKAGE_CREATE) && (
              <Button className="cursor-pointer" onClick={() => setOpen(true)}>
                <Plus className="w-3.5 h-3.5" /> Add Category
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="mt-4 dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Category
          permitEdit={permitEdit}
          openCreate={open}
          setOpenCreate={setOpen}
        />
      </div>
    </div>
  );
}
