'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Logout } from '@/lib/utils';

interface UseAccountDeactivationModalReturn {
  showModal: boolean;
  handleLogout: () => void;
  checkAccountStatus: (isActive: boolean | undefined) => void;
}

export function useAccountDeactivationModal(): UseAccountDeactivationModalReturn {
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();

  const checkAccountStatus = (isActive: boolean | undefined) => {
    if (isActive === false) {
      console.log('[AUTH] Account is deactivated, showing modal');
      setShowModal(true);
    }
  };

  const handleLogout = () => {
    console.log('[AUTH] Logging out deactivated user');
    setShowModal(false);
    Logout();
    // Use window.location to force immediate redirect without Next.js routing
    window.location.href = '/login';
  };

  return {
    showModal,
    handleLogout,
    checkAccountStatus,
  };
}
