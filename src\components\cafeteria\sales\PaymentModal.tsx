'use client';

import { useState } from 'react';
import {
  CreditCard,
  Wallet,
  ArrowRightLeft,
  Star,
  CheckCircle,
  Ticket,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { currencyFormat } from '@/lib/utils';
import { GetProfile } from '@/api/staff';
import { toTitleCase } from '@/lib/utils';

interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  totalPrice: number;
  saleType: 'general' | 'staff';
  selectedPaymentMethod: string | null;
  setSelectedPaymentMethod: (method: string | null) => void;
  cart: Array<{
    id: number;
    name: string;
    price: number;
    quantity: number;
  }>;
  onPaymentSuccess: () => void;
}

export default function PaymentModal({
  open,
  onOpenChange,
  totalPrice,
  saleType,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  cart,
  onPaymentSuccess,
}: PaymentModalProps) {
  const { profile } = GetProfile();

  const [staffId, setStaffId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [canPay, setCanPay] = useState({
    wallet: false,
    creditLimit: false,
    mealVoucher: false,
    name: '',
  });
  const [isStaffVerified, setIsStaffVerified] = useState(false);
  const [orderType, setOrderType] = useState<
    'DINE_IN' | 'TAKEAWAY' | 'DELIVERY' | null
  >(null);
  const [currentStep, setCurrentStep] = useState<
    'orderType' | 'staffVerification' | 'payment'
  >('orderType');
  const [printReceipt, setPrintReceipt] = useState(false);

  const handlePayModal = () => {
    onOpenChange(false);
    setIsStaffVerified(false);
    setStaffId('');
    setOrderType(null);
    setCurrentStep('orderType');
    setCanPay({
      wallet: false,
      creditLimit: false,
      mealVoucher: false,
      name: '',
    });
    setPrintReceipt(false);
  };

  const handleOrderTypeSelect = (type: 'DINE_IN' | 'TAKEAWAY' | 'DELIVERY') => {
    setOrderType(type);
    if (saleType === 'staff') {
      setCurrentStep('staffVerification');
    } else {
      setCurrentStep('payment');
    }
  };

  const handleStaffVerified = () => {
    setIsStaffVerified(true);
    setCurrentStep('payment');
  };

  const verifyStaff = async () => {
    if (!staffId) {
      toast.error('Enter Staff ID');
      return;
    }
    const payload = {
      code: staffId,
      amount: Number(totalPrice),
    };
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/orders/check-payment', payload);
      const data = res.data.data;
      setIsLoading(false);
      if (res.status === 200) {
        setCanPay(data);
        handleStaffVerified();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handlePaymentComplete = async () => {
    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method');
      return;
    }

    if (!orderType) {
      toast.error('Please select an order type');
      return;
    }

    try {
      setIsLoading(true);

      const orderPayload = {
        cart: cart,
        totalAmount: totalPrice,
        method: selectedPaymentMethod,
        saleType: saleType,
        type: orderType,
        servedBy: toTitleCase(profile?.data?.fullName),
        ...(saleType === 'staff' && staffId && { code: staffId }),
      };
      const res = await myApi.post('/cafeteria/orders/new', orderPayload);
      console.log(res.data.data);
      if (res.status == 200) {
        // Print receipt if checkbox is checked
        if (printReceipt) {
          try {
            const response = await myApi.get(
              `/cafeteria/orders/print-receipt/${res.data.data.orderId}`
            );
            const receiptHtml = response.data.data.receiptHtml;
            console.log(receiptHtml);

            const iframe = document.createElement('iframe');
            iframe.style.position = 'absolute';
            iframe.style.left = '-9999px';
            document.body.appendChild(iframe);

            iframe.contentDocument?.open();
            iframe.contentDocument?.write(receiptHtml);
            iframe.contentDocument?.close();

            iframe.contentWindow?.focus();
            iframe.contentWindow?.print();

            setTimeout(() => document.body.removeChild(iframe), 1000);
          } catch (printError) {
            toast.error('Order completed but receipt printing failed');
          }
        }

        setIsLoading(false);
        toast.success(res.data.data.message);
        onPaymentSuccess();
        onOpenChange(false);
        setIsStaffVerified(false);
        setStaffId('');
        setOrderType(null);
        setCurrentStep('orderType');
        setCanPay({
          wallet: false,
          creditLimit: false,
          mealVoucher: false,
          name: '',
        });
        setPrintReceipt(false);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to complete order. Please try again.');
    }
  };

  const getModalTitle = () => {
    switch (currentStep) {
      case 'orderType':
        return 'Select Order Type';
      case 'staffVerification':
        return 'Staff Verification';
      case 'payment':
        return 'Select Payment Method';
      default:
        return 'Complete Order';
    }
  };

  return (
    <Modal
      open={open}
      setOpen={(isOpen) => {
        if (!isOpen) {
          handlePayModal();
        } else {
          onOpenChange(isOpen);
        }
      }}
      title={getModalTitle()}
      description=""
      onSubmit={undefined}
      isLoading={false}
      size="sm"
    >
      <div className="space-y-6 mb-4">
        {currentStep === 'orderType' && (
          <div className="flex flex-col gap-4 py-4">
            <p className="text-sm text-gray-500">
              Please select order type to continue
            </p>
            <Select
              value={orderType || ''}
              onValueChange={(value) =>
                handleOrderTypeSelect(
                  value as 'DINE_IN' | 'TAKEAWAY' | 'DELIVERY'
                )
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select order type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DINE_IN">Dine In</SelectItem>
                <SelectItem value="TAKEAWAY">Takeaway</SelectItem>
                <SelectItem value="DELIVERY">Delivery</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {currentStep === 'staffVerification' && (
          <div className="flex flex-col gap-4 py-4">
            <p className="text-sm text-gray-500">
              Please verify staff ID to continue
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Enter Staff ID"
                value={staffId}
                onChange={(e) => setStaffId(e.target.value)}
                className="flex-1"
              />
              <Button onClick={verifyStaff} disabled={isLoading}>
                {isLoading ? 'Please wait...' : ' Verify'}
              </Button>
            </div>
          </div>
        )}

        {currentStep === 'payment' && (
          <>
            {saleType === 'staff' && (
              <h2 className="text-sm font-semibold">{canPay.name}</h2>
            )}
            <div className="grid grid-cols-2 gap-4 py-4">
              {!(
                saleType === 'staff' &&
                canPay.wallet &&
                canPay.mealVoucher
              ) && (
                <>
                  <Button
                    variant={
                      selectedPaymentMethod === 'transfer'
                        ? 'default'
                        : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('transfer')}
                  >
                    <ArrowRightLeft className="h-8 w-8" />
                    <span>Transfer</span>
                  </Button>
                  <Button
                    variant={
                      selectedPaymentMethod === 'card' ? 'default' : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('card')}
                  >
                    <CreditCard className="h-8 w-8" />
                    <span>Card</span>
                  </Button>
                </>
              )}
              {saleType === 'staff' && (
                <>
                  {canPay.mealVoucher && (
                    <Button
                      variant={
                        selectedPaymentMethod === 'voucher'
                          ? 'default'
                          : 'outline'
                      }
                      className="flex flex-col items-center justify-center h-24 gap-2"
                      onClick={() => setSelectedPaymentMethod('voucher')}
                    >
                      <Ticket className="h-8 w-8" />
                      <span>Meal Voucher</span>
                    </Button>
                  )}
                  {canPay.wallet && (
                    <Button
                      variant={
                        selectedPaymentMethod === 'wallet'
                          ? 'default'
                          : 'outline'
                      }
                      className="flex flex-col items-center justify-center h-24 gap-2"
                      onClick={() => setSelectedPaymentMethod('wallet')}
                    >
                      <Wallet className="h-8 w-8" />
                      <span>Wallet</span>
                    </Button>
                  )}
                  {canPay.creditLimit && (
                    <Button
                      variant={
                        selectedPaymentMethod === 'credit'
                          ? 'default'
                          : 'outline'
                      }
                      className="flex flex-col items-center justify-center h-24 gap-2"
                      onClick={() => setSelectedPaymentMethod('credit')}
                    >
                      <Star className="h-8 w-8" />
                      <span>Credit</span>
                    </Button>
                  )}
                </>
              )}
            </div>
          </>
        )}

        <div className="flex justify-between items-center py-2 border-t border-b">
          <span className="font-bold">Total:</span>
          <span className="font-bold text-lg">
            {currencyFormat(totalPrice)}
          </span>
        </div>

        {currentStep === 'payment' && (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="printReceipt"
              checked={printReceipt}
              onChange={(e) => setPrintReceipt(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label
              htmlFor="printReceipt"
              className="text-sm text-gray-700 dark:text-gray-300"
            >
              Print Receipt
            </label>
          </div>
        )}

        <div className="flex justify-end gap-2 pb-2">
          <Button variant="outline" onClick={handlePayModal}>
            Cancel
          </Button>
          {currentStep === 'orderType' && (
            <Button
              disabled={!orderType}
              onClick={() => orderType && handleOrderTypeSelect(orderType)}
            >
              Continue
            </Button>
          )}
          {currentStep === 'payment' && (
            <Button
              disabled={!selectedPaymentMethod || isLoading}
              onClick={handlePaymentComplete}
            >
              {isLoading ? 'Processing...' : 'Complete Payment'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
