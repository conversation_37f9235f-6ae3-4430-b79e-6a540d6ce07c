import { z } from "zod";

export const ReferralFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  gender: z.string().min(1, "Gender is required"),
  referralType: z.string().min(1, "Referral type is required"),
  specialty: z.string().optional(),
  reason: z.string().min(1, "Reason for referral is required"),
  urgency: z.string().min(1, "Urgency is required"),
  receivingEntity: z.string().min(1, "Receiving entity is required"),
});
