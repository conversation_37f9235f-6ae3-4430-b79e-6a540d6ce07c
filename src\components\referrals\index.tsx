'use client';

import { ArrowRightLeft, Users, Send  } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Referral from './referral/data-table';
import NewReferralForm from './referral/NewReferralForm';
import { Paths } from '@/components/navigations/data';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useState } from 'react';
import { GetProfile } from '@/api/staff';



export default function ReferringPage() {
  const {profile} = GetProfile();
  const canRefer = hasPermission(PERMISSIONS.REFERRAL_CREATE);
  const canView = hasPermission(PERMISSIONS.REFERRAL_VIEW);
  const [showNewReferralForm, setShowNewReferralForm] = useState(false);

  const data = profile?.data?.location;

  if (showNewReferralForm) {
    return (
      <NewReferralForm 
        onBack={() => setShowNewReferralForm(false)} 
        props={data.id}
      />
    );
  }

  return (
    <>
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <ArrowRightLeft className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Referral Management
        </h2>
        <div>
          <div className="flex flex-wrap gap-2">
          { canRefer && 
         (
              <Button onClick={() => setShowNewReferralForm(true)} className="cursor-pointer">
                <Send  className="w-3.5 h-3.5" /> Refer Patient
              </Button>
            )
            }
         {canView &&   (<Link href={`${Paths.Referral}/referring-entities`}>
              <Button variant="outline" className="cursor-pointer">
                <Users className="w-3.5 h-3.5" /> Referring Entities
              </Button>
            </Link>)}
          </div>
        </div>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Referral />
      </div>
    </>
  );
}
