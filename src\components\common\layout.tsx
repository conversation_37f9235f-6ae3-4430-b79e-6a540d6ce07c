'use client';

import type { ReactNode } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTheme } from 'next-themes';
import { SWRConfig } from 'swr';
import { toast } from 'sonner';

import Sidebar from '../navigations/sideBar';
import TopNav from './top-nav';
import { fetcher } from '@/api/fetcher';
import { NetworkError } from './network-error';
import { NetworkStatusIndicator, NetworkStatusBanner } from './network-status';
import { useProfileVerification } from '@/hooks/useProfileVerification';
import useIsLoggedIn from '@/hooks/isLoggedIn';
import { Logout } from '@/lib/utils';
import {
  pathPermissionMap,
  findFirstAccessibleRoute,
} from '@/lib/route-permissions';
import { useSidebar } from '@/contexts/SidebarContext';
import { AccountDeactivationModal } from './account-deactivation-modal';
import { ProfileVerificationModal } from './profile-verification-modal';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const { theme } = useTheme();
  const { isCollapsed } = useSidebar();
  const isLoggedIn = useIsLoggedIn();
  const router = useRouter();
  const pathname = usePathname();

  const [mounted, setMounted] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  const {
    isLoading: loadingProfile,
    isSuccess: profileVerified,
    isError: profileError,
    isTimeout: profileTimeout,
    error: profileErrorDetails,
    permissions = [],
    showDeactivationModal,
    handleDeactivationLogout,
    showProfileModal,
    isRetrying,
    handleRetry,
    handleProfileModalLogout,
  } = useProfileVerification(20000);

  const normalizedPath =
    pathname.endsWith('/') && pathname !== '/'
      ? pathname.slice(0, -1)
      : pathname;

  const hasHandledRedirect = useRef(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    setRedirecting(false);
    hasHandledRedirect.current = false;
  }, [pathname]);

  useEffect(() => {
    if (
      !mounted ||
      !isLoggedIn ||
      loadingProfile ||
      !profileVerified ||
      redirecting ||
      hasHandledRedirect.current ||
      permissions.length === 0
    ) {
      return;
    }

    const requiredPermission = pathPermissionMap[normalizedPath];

    // If no permission is required or user has it, do nothing
    if (!requiredPermission || permissions.includes(requiredPermission)) {
      return;
    }

    // ✅ Use correct function to get fallback route by priority
    const fallbackRoute = findFirstAccessibleRoute(permissions);

    hasHandledRedirect.current = true;

    if (fallbackRoute && fallbackRoute !== normalizedPath) {
      setRedirecting(true);
      router.replace(fallbackRoute);
    } else {
      toast.error('You no longer have access. Logging out...');
      Logout();
    }
  }, [
    mounted,
    isLoggedIn,
    loadingProfile,
    profileVerified,
    permissions,
    normalizedPath,
    redirecting,
    router,
  ]);

  if (profileTimeout) {
    return (
      <NetworkError
        isTimeout
        error={profileErrorDetails as Error}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (
    !mounted ||
    !isLoggedIn ||
    loadingProfile ||
    redirecting ||
    (isLoggedIn && profileVerified && !permissions.length)
  ) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-primary border-t-2 border-b-2" />
          <p className="text-sm text-muted-foreground">
            Verifying permissions...
          </p>
        </div>
      </div>
    );
  }

  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnFocus: true,
        revalidateOnReconnect: true,
        // Global error handling
        shouldRetryOnError: (error) => {
          // Don't retry on network errors or when offline
          if (error?.isNetworkError || !navigator.onLine) {
            return false;
          }
          // Don't retry on 4xx errors (client errors)
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          return true;
        },
        errorRetryInterval: navigator.onLine ? 5000 : 30000,
        errorRetryCount: navigator.onLine ? 3 : 0,
      }}
    >
      <div className={`mx-auto h-screen ${theme === 'dark' ? 'dark' : ''}`}>
        <NetworkStatusBanner />
        <Sidebar />
        <header
          className={`fixed top-0 right-0 left-0 h-16 bg-white dark:bg-[#0F0F12] border-b border-gray-200 dark:border-[#1F1F23] z-20 transition-all duration-200 ${isCollapsed ? 'lg:left-18' : 'lg:left-54'}`}
        >
          <TopNav />
        </header>
        <main
          className={`pt-16 pl-0 min-h-screen overflow-y-auto bg-white dark:bg-[#0F0F12] transition-all duration-200 ${isCollapsed ? 'lg:pl-20' : 'lg:pl-56'}`}
        >
          <div className="p-4">{children}</div>
        </main>
        <NetworkStatusIndicator />

        {/* Account Deactivation Modal */}
        <AccountDeactivationModal
          open={showDeactivationModal || false}
          onLogout={handleDeactivationLogout || (() => {})}
        />

        {/* Profile Verification Modal */}
        <ProfileVerificationModal
          open={showProfileModal || false}
          isRetrying={isRetrying || false}
          onRetry={handleRetry || (() => {})}
          onLogout={handleProfileModalLogout || (() => {})}
        />
      </div>
    </SWRConfig>
  );
}
