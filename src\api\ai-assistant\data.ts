import useSWR from 'swr';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

// Types for AI Assistant
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
  metadata?: {
    model?: string;
    tokens?: number;
    responseTime?: number;
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  isActive: boolean;
  metadata?: {
    totalMessages: number;
    totalTokens: number;
    model: string;
  };
}

export interface AIResponse {
  message: Message;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  responseTime: number;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: {
    patientId?: string;
    departmentId?: string;
    specialty?: string;
  };
  settings?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export interface AISettings {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  enableContext: boolean;
  enableMemory: boolean;
}

// API Functions

// Get all chat sessions for current user
export const GetChatSessions = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/ai-assistant/sessions?${qs.toString()}`
  );

  return {
    sessions: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    error,
    mutate,
  };
};

// Get a specific chat session
export const GetChatSession = (sessionId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    sessionId ? `/ai-assistant/sessions/${sessionId}` : null
  );

  return {
    session: data?.data as ChatSession,
    isLoading,
    error,
    mutate,
  };
};

// Create new chat session
export const createChatSession = async (title?: string) => {
  try {
    const response = await myApi.post('/ai-assistant/sessions', {
      title: title || 'New Chat',
    });
    return response.data;
  } catch (error) {
    console.error('Error creating chat session:', error);
    toast.error('Failed to create chat session');
    throw error;
  }
};

// Send message to AI Assistant
export const sendMessage = async (
  chatRequest: ChatRequest
): Promise<AIResponse> => {
  try {
    const response = await myApi.post('/ai-assistant/chat', chatRequest);
    return response.data;
  } catch (error) {
    console.error('Error sending message to AI:', error);
    toast.error('Failed to send message to AI Assistant');
    throw error;
  }
};

// Update chat session title
export const updateChatSessionTitle = async (
  sessionId: string,
  title: string
) => {
  try {
    const response = await myApi.patch(`/ai-assistant/sessions/${sessionId}`, {
      title,
    });
    toast.success('Chat title updated');
    return response.data;
  } catch (error) {
    console.error('Error updating chat session title:', error);
    toast.error('Failed to update chat title');
    throw error;
  }
};

// Delete chat session
export const deleteChatSession = async (sessionId: string) => {
  try {
    const response = await myApi.delete(`/ai-assistant/sessions/${sessionId}`);
    toast.success('Chat session deleted');
    return response.data;
  } catch (error) {
    console.error('Error deleting chat session:', error);
    toast.error('Failed to delete chat session');
    throw error;
  }
};

// Clear all messages in a session
export const clearChatSession = async (sessionId: string) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/sessions/${sessionId}/clear`
    );
    toast.success('Chat cleared');
    return response.data;
  } catch (error) {
    console.error('Error clearing chat session:', error);
    toast.error('Failed to clear chat');
    throw error;
  }
};

// Get AI Assistant settings
export const GetAISettings = () => {
  const { data, error, isLoading, mutate } = useSWR('/ai-assistant/settings');

  return {
    settings: data?.data as AISettings,
    isLoading,
    error,
    mutate,
  };
};

// Update AI Assistant settings
export const updateAISettings = async (settings: Partial<AISettings>) => {
  try {
    const response = await myApi.patch('/ai-assistant/settings', settings);
    toast.success('AI Assistant settings updated');
    return response.data;
  } catch (error) {
    console.error('Error updating AI settings:', error);
    toast.error('Failed to update AI settings');
    throw error;
  }
};

// Get conversation analytics
export const GetConversationAnalytics = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/ai-assistant/analytics?${qs.toString()}`
  );

  return {
    analytics: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Export chat session
export const exportChatSession = async (
  sessionId: string,
  format: 'pdf' | 'txt' | 'json'
) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/sessions/${sessionId}/export`,
      {
        format,
      }
    );

    // Handle file download
    const blob = new Blob([response.data], {
      type: format === 'pdf' ? 'application/pdf' : 'text/plain',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chat-session-${sessionId}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('Chat session exported successfully');
    return response.data;
  } catch (error) {
    console.error('Error exporting chat session:', error);
    toast.error('Failed to export chat session');
    throw error;
  }
};

// Search chat history
export const searchChatHistory = async (query: string, sessionId?: string) => {
  try {
    const response = await myApi.post('/ai-assistant/search', {
      query,
      sessionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error searching chat history:', error);
    toast.error('Failed to search chat history');
    throw error;
  }
};

// Get suggested prompts based on context
export const getSuggestedPrompts = async (context?: {
  patientId?: string;
  departmentId?: string;
  specialty?: string;
}) => {
  try {
    const response = await myApi.post('/ai-assistant/suggestions', { context });
    return response.data;
  } catch (error) {
    console.error('Error getting suggested prompts:', error);
    return [];
  }
};

// Rate AI response
export const rateAIResponse = async (
  messageId: string,
  rating: 'positive' | 'negative',
  feedback?: string
) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/messages/${messageId}/rate`,
      {
        rating,
        feedback,
      }
    );
    toast.success('Thank you for your feedback');
    return response.data;
  } catch (error) {
    console.error('Error rating AI response:', error);
    toast.error('Failed to submit rating');
    throw error;
  }
};

// Get AI model status
export const GetAIModelStatus = () => {
  const { data, error, isLoading, mutate } = useSWR('/ai-assistant/status');

  return {
    status: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Mock function for development - simulates OpenAI API call
export const mockAIResponse = async (message: string): Promise<string> => {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1000 + Math.random() * 2000)
  );

  const input = message.toLowerCase();

  // Patient-related queries
  if (input.includes('patient') && input.includes('discharge')) {
    return 'For patient discharge: Ensure all discharge criteria are met, medications reconciled, follow-up appointments scheduled, and discharge summary completed. Patient education materials should be provided. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('patient') && input.includes('admission')) {
    return 'Patient admission checklist: Verify insurance, complete medical history, conduct physical assessment, order necessary labs/imaging, establish care plan, and ensure bed assignment. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('patient') || input.includes('medical record')) {
    return 'For patient information: Always verify patient identity, ensure all data protection laws compliance, and access only necessary medical records. Note: This is a test response - full AI integration pending.';
  }

  // Emergency and critical care
  else if (input.includes('code blue') || input.includes('cardiac arrest')) {
    return 'CODE BLUE Protocol: Call rapid response team, begin CPR if needed, prepare crash cart, establish IV access, monitor vitals, and document all interventions. Follow ACLS guidelines. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('emergency') || input.includes('trauma')) {
    return 'Emergency protocol: Assess ABCs (Airway, Breathing, Circulation), triage severity, notify appropriate specialists, prepare for interventions, and ensure family notification if needed. Note: This is a test response - full AI integration pending.';
  }

  // Medications and pharmacy
  else if (input.includes('medication') && input.includes('allergy')) {
    return 'Medication allergy protocol: Verify allergy history, check for cross-reactions, document severity, use alternative medications, and ensure allergy alerts are visible in patient chart. Note: This is a test response - full AI integration pending.';
  } else if (
    input.includes('medication') ||
    input.includes('drug') ||
    input.includes('prescription')
  ) {
    return 'Medication management: Verify five rights (patient, drug, dose, route, time), check for interactions, review allergies, and document administration. Consult pharmacy for complex cases. Note: This is a test response - full AI integration pending.';
  }

  // Laboratory and diagnostics
  else if (input.includes('lab') && input.includes('critical')) {
    return 'Critical lab values require immediate notification: Contact physician within 30 minutes, document notification, repeat if necessary, and monitor patient closely for clinical changes. Note: This is a test response - full AI integration pending.';
  } else if (
    input.includes('lab') ||
    input.includes('test') ||
    input.includes('blood work')
  ) {
    return 'Laboratory procedures: Verify patient identity, use proper collection techniques, label specimens correctly, and ensure timely transport. Normal ranges vary by lab and patient demographics. Note: This is a test response - full AI integration pending.';
  }

  // Procedures and protocols
  else if (input.includes('infection control') || input.includes('isolation')) {
    return 'Infection control: Use appropriate PPE, follow hand hygiene protocols, implement isolation precautions as needed, and report infectious diseases per hospital policy. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('procedure') || input.includes('protocol')) {
    return 'Hospital procedures: Obtain informed consent, verify patient identity, follow sterile technique when applicable, document thoroughly, and monitor for complications. Note: This is a test response - full AI integration pending.';
  }

  // Scheduling and administrative
  else if (input.includes('schedule') || input.includes('appointment')) {
    return 'Scheduling guidelines: Verify insurance authorization, coordinate with departments, confirm patient availability, send reminders, and maintain waitlists for cancellations. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('documentation') || input.includes('charting')) {
    return 'Documentation standards: Chart in real-time, use objective language, include patient responses, sign all entries, and ensure legibility. Follow hospital documentation policies. Note: This is a test response - full AI integration pending.';
  }

  // Staff and workflow
  else if (input.includes('handoff') || input.includes('shift change')) {
    return 'Shift handoff protocol: Use SBAR format (Situation, Background, Assessment, Recommendation), review critical patients first, confirm orders, and address questions before leaving. Note: This is a test response - full AI integration pending.';
  } else if (
    input.includes('staff') ||
    input.includes('nurse') ||
    input.includes('doctor')
  ) {
    return 'Staff communication: Use secure messaging systems, escalate concerns appropriately, maintain professional boundaries, and follow chain of command for issues. Note: This is a test response - full AI integration pending.';
  }

  // Quality and safety
  else if (input.includes('incident') || input.includes('safety')) {
    return 'Safety incident reporting: Document immediately, notify supervisor, complete incident report, preserve evidence if applicable, and follow up on corrective actions. Note: This is a test response - full AI integration pending.';
  } else if (input.includes('quality') || input.includes('improvement')) {
    return 'Quality improvement: Identify opportunities, collect data, implement evidence-based changes, monitor outcomes, and engage multidisciplinary teams in improvement efforts. Note: This is a test response - full AI integration pending.';
  }

  // Default response
  else {
    return "I'm here to assist with hospital operations, patient care, medical procedures, safety protocols, and administrative tasks. Please specify your question for more targeted assistance. Note: This is a mock response - full AI integration pending.";
  }
};
