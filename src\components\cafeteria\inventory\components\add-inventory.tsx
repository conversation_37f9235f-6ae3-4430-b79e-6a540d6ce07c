import React, { useState } from 'react';
import { InputField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  InventoryFormSchema,
  Inventory,
} from '@/components/validations/inventory';
import { CustomSelectForm } from '@/components/common/another';
import { ModalProps } from '@/components/types';
import { GetAllInventoryCat } from '@/api/cafeteria/data';

const CreateInventory: React.FC<ModalProps> = ({
  setOpen,
  mutate,
  open,
  profile,
}) => {
  const { category } = GetAllInventoryCat();
  const [isLoading, setIsLoading] = useState(false);

  const inventoryCategoryData = category?.data;

  const units = [
    {
      id: 'Gram',
      name: 'Gram',
    },
    {
      id: 'Kg',
      name: 'Kg',
    },
    {
      id: 'Liter',
      name: '<PERSON><PERSON>',
    },
    {
      id: 'Supplies',
      name: 'Supplies',
    },
    {
      id: 'Pack',
      name: 'Pack',
    },
    {
      id: 'Carton',
      name: 'Carton',
    },
    {
      id: 'Gallon',
      name: 'Gallon',
    },
  ];

  const form = useForm<Inventory>({
    resolver: zodResolver(InventoryFormSchema),
    defaultValues: {
      minimumStock: '',
      currentStock: '',
      name: '',
    },
  });

  const onSubmit = async (data: Inventory) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/inventory/add-new', {
        itemName: data.name,
        category: data.category,
        unit: data.unit,
        minimumStock: Number(data.minimumStock),
        currentStock: Number(data.currentStock),
        createdBy: profile?.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to create inventory item');
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add New Inventory Item"
      description="Create a new inventory item to add to the list"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <InputField
              control={form.control}
              name="name"
              label="Inventory Name"
              placeholder="Enter inventory name"
              type="text"
            />
            <CustomSelectForm
              control={form.control}
              name="unit"
              label="Unit"
              placeholder="Select unit"
              options={units}
            />
            <InputField
              control={form.control}
              name="minimumStock"
              label="Minimum Stock"
              placeholder=""
              type="number"
            />
            <InputField
              control={form.control}
              name="currentStock"
              label="Current Stock"
              placeholder=""
              type="number"
            />
          </FormRow>
          <CustomSelectForm
            control={form.control}
            name="category"
            label="Inventory Category"
            placeholder="Select inventory category"
            options={inventoryCategoryData}
          />
        </form>
      </Form>
    </Modal>
  );
};

export default CreateInventory;
