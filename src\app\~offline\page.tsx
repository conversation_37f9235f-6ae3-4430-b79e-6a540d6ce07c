import type { Metadata } from 'next';
import Image from 'next/image';
import { WifiOff } from 'lucide-react';
import OfflineActions from '@/components/Offline/OfflineActions';

export const metadata: Metadata = {
  title: "You're Offline | Cedarcrest Hospitals",
  description:
    'You are currently offline. Please check your internet connection.',
  robots: { index: false, follow: false },
};

export default function OfflinePage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen px-4 py-12 text-center bg-gray-50">
      <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
        {/* Logo */}
        <div className="relative w-48 h-16 mx-auto mb-6">
          <Image
            src="/logo.png"
            alt="Cedarcrest Hospitals"
            fill
            sizes="(max-width: 768px) 100vw, 192px"
            style={{ objectFit: 'contain' }}
            priority
          />
        </div>

        {/* Offline Icon */}
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-gray-100 rounded-full">
            <WifiOff className="w-12 h-12 text-primary" />
          </div>
        </div>

        {/* Content */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          You're Offline
        </h1>
        <p className="text-gray-600 mb-6">
          It seems you've lost your internet connection. The Cedarcrest
          Hospitals Limited Innovation Suite requires an internet connection to
          function.
        </p>

        {/* Client-side Actions Component */}
        <OfflineActions />

        {/* Cached Content Notice */}
        <p className="text-xs text-gray-500 mt-8">
          Some features may be available offline if you've visited them before.
        </p>
      </div>
    </div>
  );
}
