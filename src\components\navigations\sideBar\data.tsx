import React from 'react';
import { Paths } from '../data';
import {
  HandCoins,
  Gem,
  MessageCircleMore,
  Home,
  Percent,
  Wallet,
  ArrowRightLeft,
  MapPin,
  Coffee,
  Lightbulb,
  BookOpen,
  Mailbox,
} from 'lucide-react';

interface SidebarItem {
  label: string;
  url: string;
  icon: React.ElementType;
}

interface SidebarSection {
  name: string;
  children: SidebarItem[];
}

export const LeftSidebarData: SidebarSection[] = [
  {
    name: 'OVERVIEW',
    children: [
      {
        label: 'Dashboard',
        url: Paths.Dashboard,
        icon: Home,
      },
      {
        label: 'Packages',
        url: Paths.Packages,
        icon: HandCoins,
      },
      {
        label: 'Cafeteria',
        url: Paths.Cafeteria,
        icon: Coffee,
      },
      {
        label: 'Feedbacks',
        url: Paths.Feedbacks,
        icon: MessageCircleMore,
      },
      {
        label: 'Referrals',
        url: Paths.Referral,
        icon: ArrowRightLeft,
      },
      {
        label: 'Suggestion Box',
        url: Paths.SuggestionBox,
        icon: Mailbox,
      },
    ],
  },
  {
    name: 'FINANCE',
    children: [
      {
        label: 'Discounts',
        url: Paths.Discounts,
        icon: Percent,
      },
      {
        label: 'Transactions',
        url: Paths.Transactions,
        icon: Wallet,
      },
    ],
  },
  {
    name: 'MANAGE',
    children: [
      {
        label: 'Rewards',
        url: Paths.Rewards,
        icon: Gem,
      },
      {
        label: 'Locations',
        url: Paths.Location,
        icon: MapPin,
      },
    ],
  },
];

export const LeftSidebarFooterData: SidebarItem[] = [
  {
    label: 'Innovation Hub',
    url: Paths.InnovationHub,
    icon: Lightbulb,
  },
  {
    label: 'Process Library',
    url: Paths.ProcessDictionary,
    icon: BookOpen,
  },
  // {
  //   label: 'CHIS',
  //   url: Paths.CHIS,
  //   icon: Shield,
  // },
  // {
  //   label: 'AI Assistant',
  //   url: Paths.AIAssistant,
  //   icon: Bot,
  // },
];
