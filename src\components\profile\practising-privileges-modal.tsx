'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { UserProps } from './types';
import { Button } from '@/components/ui/button';

import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';
import { PDFDocument, rgb } from 'pdf-lib';
import { FileText, Upload, Download } from 'lucide-react';

interface PractisingPrivilegesModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: UserProps;
}

interface SignatureData {
  file: File | null;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
}

interface TextFieldData {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  fontSize: number;
  isEditing: boolean;
}

export const PractisingPrivilegesModal: React.FC<
  PractisingPrivilegesModalProps
> = ({ open, setOpen, user }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [originalPdfUrl, setOriginalPdfUrl] = useState<string | null>(null);
  const [signature, setSignature] = useState<SignatureData>({
    file: null,
    x: 100,
    y: 400,
    width: 120,
    height: 60,
    rotation: 0,
  });
  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);
  const [textFields, setTextFields] = useState<TextFieldData[]>([]);
  const [isAddingText, setIsAddingText] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pdfContainerRef = useRef<HTMLDivElement>(null);

  // Load original PDF when modal opens
  useEffect(() => {
    if (open && !originalPdfUrl) {
      loadOriginalPdf();
    }
  }, [open]);

  // Handle ESC key to cancel add text mode
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isAddingText) {
        setIsAddingText(false);
        toast.info('Add text mode cancelled');
      }
    };

    if (open) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [open, isAddingText]);

  // Cleanup PDF URLs when component unmounts or modal closes
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
      if (originalPdfUrl) {
        URL.revokeObjectURL(originalPdfUrl);
      }
      if (signaturePreview) {
        URL.revokeObjectURL(signaturePreview);
      }
    };
  }, [pdfUrl, originalPdfUrl, signaturePreview]);

  useEffect(() => {
    if (!open) {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
      if (originalPdfUrl) {
        URL.revokeObjectURL(originalPdfUrl);
        setOriginalPdfUrl(null);
      }
    }
  }, [open, pdfUrl, originalPdfUrl]);

  const loadOriginalPdf = async () => {
    try {
      setIsLoading(true);
      const pdfBytes = await loadPdfTemplate();
      const blob = new Blob([new Uint8Array(pdfBytes)], {
        type: 'application/pdf',
      });
      const url = URL.createObjectURL(blob);
      setOriginalPdfUrl(url);
    } catch (error) {
      console.error('Error loading original PDF:', error);
      toast.error('Failed to load PDF template');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPdfTemplate = async (): Promise<Uint8Array> => {
    try {
      // Load the privileges.pdf from the lib folder via API
      const response = await fetch('/api/pdf-template');
      if (response.ok) {
        console.log('Successfully loading privileges.pdf from lib folder');
        return new Uint8Array(await response.arrayBuffer());
      }

      throw new Error('privileges.pdf not found in lib folder');
    } catch (error) {
      console.error('Error loading privileges.pdf from lib folder:', error);
      throw new Error('Failed to load privileges.pdf from lib folder');
    }
  };

  const handleSignatureUpload = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSignature((prev) => ({ ...prev, file }));

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setSignaturePreview(previewUrl);

      toast.success('Signature uploaded successfully');
    } else {
      toast.error('Please upload a valid image file');
    }
  };

  const handlePdfClick = (event: React.MouseEvent) => {
    if (!isAddingText || !pdfContainerRef.current) return;

    const rect = pdfContainerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newTextField: TextFieldData = {
      id: `text-${Date.now()}`,
      x,
      y,
      width: 200,
      height: 30,
      text: '',
      fontSize: 12,
      isEditing: true,
    };

    setTextFields((prev) => [...prev, newTextField]);
    setIsAddingText(false);
  };

  const updateTextField = (id: string, updates: Partial<TextFieldData>) => {
    setTextFields((prev) =>
      prev.map((field) => (field.id === id ? { ...field, ...updates } : field))
    );
  };

  const deleteTextField = (id: string) => {
    setTextFields((prev) => prev.filter((field) => field.id !== id));
  };

  const toggleAddTextMode = () => {
    setIsAddingText(!isAddingText);
    if (!isAddingText) {
      toast.info('Click anywhere on the PDF to add a text field');
    }
  };

  const generateFinalPdf = async () => {
    if (!originalPdfUrl) {
      toast.error('PDF template not loaded');
      return;
    }

    try {
      setIsLoading(true);

      const existingPdfBytes = await loadPdfTemplate();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      // Get the first page
      const pages = pdfDoc.getPages();
      const firstPage = pages[0];
      const pdfHeight = firstPage.getHeight();

      // Add custom text fields
      textFields.forEach((field) => {
        if (field.text.trim()) {
          // Convert screen coordinates to PDF coordinates
          const pdfY = pdfHeight - field.y - field.height;

          firstPage.drawText(field.text, {
            x: field.x,
            y: pdfY + field.height / 2 - field.fontSize / 2, // Center vertically
            size: field.fontSize,
            color: rgb(0, 0, 0),
          });
        }
      });

      // Add signature if uploaded
      if (signature.file) {
        try {
          const signatureBytes = await signature.file.arrayBuffer();
          let signatureImage;

          if (signature.file.type === 'image/png') {
            signatureImage = await pdfDoc.embedPng(signatureBytes);
          } else if (
            signature.file.type === 'image/jpeg' ||
            signature.file.type === 'image/jpg'
          ) {
            signatureImage = await pdfDoc.embedJpg(signatureBytes);
          }

          if (signatureImage) {
            // Convert screen coordinates to PDF coordinates
            const pdfHeight = firstPage.getHeight();
            const pdfY = pdfHeight - signature.y - signature.height;

            firstPage.drawImage(signatureImage, {
              x: signature.x,
              y: pdfY,
              width: signature.width,
              height: signature.height,
            });
          }
        } catch (signatureError) {
          console.error('Error embedding signature:', signatureError);
          toast.error('Failed to embed signature');
        }
      }

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([new Uint8Array(pdfBytes)], {
        type: 'application/pdf',
      });
      setPdfBlob(blob);

      // Automatically download the PDF
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `privileges-${user.fullName?.replace(/\s+/g, '-') || 'form'}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success(
        'PDF downloaded successfully! Fill it out and then submit.'
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF');
    } finally {
      setIsLoading(false);
    }
  };

  const submitApplication = async () => {
    try {
      setIsLoading(true);

      // Generate PDF with custom text fields and signature
      const existingPdfBytes = await loadPdfTemplate();
      const pdfDoc = await PDFDocument.load(existingPdfBytes);

      const pages = pdfDoc.getPages();
      const firstPage = pages[0];
      const pdfHeight = firstPage.getHeight();

      // Add custom text fields
      textFields.forEach((field) => {
        if (field.text.trim()) {
          // Convert screen coordinates to PDF coordinates
          const pdfY = pdfHeight - field.y - field.height;

          firstPage.drawText(field.text, {
            x: field.x,
            y: pdfY + field.height / 2 - field.fontSize / 2, // Center vertically
            size: field.fontSize,
            color: rgb(0, 0, 0),
          });
        }
      });

      // Add signature if uploaded
      if (signature.file) {
        try {
          const signatureBytes = await signature.file.arrayBuffer();
          let signatureImage;

          if (signature.file.type === 'image/png') {
            signatureImage = await pdfDoc.embedPng(signatureBytes);
          } else if (
            signature.file.type === 'image/jpeg' ||
            signature.file.type === 'image/jpg'
          ) {
            signatureImage = await pdfDoc.embedJpg(signatureBytes);
          }

          if (signatureImage) {
            const pdfWidth = firstPage.getWidth();
            firstPage.drawImage(signatureImage, {
              x: pdfWidth - 150,
              y: 50,
              width: 120,
              height: 60,
            });
          }
        } catch (signatureError) {
          console.error('Error embedding signature:', signatureError);
        }
      }

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([new Uint8Array(pdfBytes)], {
        type: 'application/pdf',
      });

      const formDataToSubmit = new FormData();
      formDataToSubmit.append(
        'privilegesPdf',
        blob,
        `privileges-${user.id}.pdf`
      );
      formDataToSubmit.append('userId', user.id || '');
      formDataToSubmit.append('fullName', user.fullName || '');
      formDataToSubmit.append(
        'specialty',
        user.doctorProfile?.specialty?.name || ''
      );
      formDataToSubmit.append('email', user.email || '');
      formDataToSubmit.append('phoneNumber', user.phoneNumber || '');

      const response = await myApi.patch('/staff/update', formDataToSubmit, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 200) {
        toast.success('Privileges application submitted successfully');
        setOpen(false);
        // Reset form
        setPdfBlob(null);
        setPdfUrl(null);
        setSignature({
          file: null,
          x: 100,
          y: 400,
          width: 120,
          height: 60,
          rotation: 0,
        });
      }
    } catch (error: any) {
      console.error('Error submitting application:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to submit application'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Practising Privileges Application"
      description="Fill out the practising privileges form and submit your application"
      size="xl"
    >
      <div className="space-y-4 lg:space-y-6">
        {/* Instructions for DocuSign-Style PDF Interaction */}
        <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">
                DocuSign-Style PDF Editing
              </h4>
              <p className="text-sm text-blue-700 mb-2">
                <strong>Two Ways to Fill the Form:</strong> Use built-in PDF
                fields OR add custom text anywhere on the document.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="text-blue-600 space-y-1">
                  <p className="font-medium">📝 Built-in PDF Fields:</p>
                  <p>• Click on existing form fields to type</p>
                  <p>• Check boxes by clicking them</p>
                  <p>• Use PDF toolbar for navigation</p>
                </div>
                <div className="text-green-600 space-y-1">
                  <p className="font-medium">✨ Custom Text Fields:</p>
                  <p>• Click "Add Text" button</p>
                  <p>• Click anywhere on PDF to add text</p>
                  <p>• Edit, resize, or delete custom fields</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Optional Signature Upload */}
        <div className="border-b pb-4">
          <Label htmlFor="signature">Digital Signature (Optional)</Label>
          <div className="mt-2 space-y-3">
            <div className="flex items-center gap-4">
              <input
                ref={fileInputRef}
                type="file"
                id="signature"
                accept="image/*"
                onChange={handleSignatureUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Upload Signature (Optional)
              </Button>
              {signature.file && (
                <span className="text-sm text-green-600">
                  ✓ {signature.file.name}
                </span>
              )}
            </div>

            {signature.file && (
              <div className="text-xs text-gray-600 bg-amber-50 p-2 rounded border border-amber-200">
                ⚠️ <strong>Note:</strong> Signature will be added when you
                download/submit the form. You can also sign directly on the PDF
                if it has a signature field.
              </div>
            )}
          </div>
        </div>

        {/* Interactive PDF Viewer */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
            <h3 className="text-lg font-semibold">
              Privileges Form - Interactive PDF
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={toggleAddTextMode}
                variant={isAddingText ? 'default' : 'outline'}
                size="sm"
                className={
                  isAddingText
                    ? 'bg-green-600 hover:bg-green-700 animate-pulse'
                    : ''
                }
              >
                {isAddingText
                  ? '✓ Click PDF to Add Text'
                  : `+ Add Text ${textFields.length > 0 ? `(${textFields.length})` : ''}`}
              </Button>

              {(originalPdfUrl || pdfUrl) && (
                <Button
                  onClick={() => {
                    const url = originalPdfUrl || pdfUrl;
                    if (url) window.open(url, '_blank');
                  }}
                  variant="outline"
                  size="sm"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Open in New Tab
                </Button>
              )}

              <Button
                onClick={generateFinalPdf}
                disabled={isLoading}
                variant="outline"
              >
                <Download className="w-4 h-4 mr-2" />
                {isLoading ? 'Preparing...' : 'Download PDF'}
              </Button>

              <Button onClick={submitApplication} disabled={isLoading}>
                <Upload className="w-4 h-4 mr-2" />
                {isLoading ? 'Submitting...' : 'Submit Application'}
              </Button>
            </div>
          </div>

          {/* Interactive PDF Display Container */}
          <div
            ref={pdfContainerRef}
            className={`relative border-2 rounded-lg overflow-hidden bg-white ${
              isAddingText ? 'border-green-400' : 'border-blue-200'
            }`}
            style={{
              height: 'clamp(700px, 85vh, 1000px)',
              minHeight: '700px',
            }}
          >
            {isLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-20">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">
                    Loading privileges.pdf from lib folder...
                  </p>
                </div>
              </div>
            )}

            {/* PDF Layer */}
            {(pdfUrl || originalPdfUrl) && (
              <iframe
                src={`${pdfUrl || originalPdfUrl}#toolbar=1&navpanes=0&scrollbar=1&page=1&view=FitH`}
                className="w-full h-full border-0"
                title="Interactive Privileges PDF"
                style={{
                  pointerEvents: isAddingText ? 'none' : 'auto',
                }}
              />
            )}

            {/* Clickable Overlay for Adding Text Fields */}
            {isAddingText && (
              <div
                className="absolute inset-0 z-10 cursor-crosshair"
                onClick={handlePdfClick}
                style={{ backgroundColor: 'rgba(0, 255, 0, 0.05)' }}
              />
            )}

            {/* Text Field Overlays */}
            {textFields.map((field) => (
              <div
                key={field.id}
                className="absolute border-2 border-blue-500 bg-white shadow-lg z-30 group hover:border-blue-600 transition-colors"
                style={{
                  left: field.x,
                  top: field.y,
                  width: field.width,
                  height: field.height,
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {field.isEditing ? (
                  <input
                    type="text"
                    value={field.text}
                    onChange={(e) =>
                      updateTextField(field.id, { text: e.target.value })
                    }
                    onBlur={() =>
                      updateTextField(field.id, { isEditing: false })
                    }
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        updateTextField(field.id, { isEditing: false });
                      }
                      if (e.key === 'Escape') {
                        updateTextField(field.id, { isEditing: false });
                      }
                    }}
                    className="w-full h-full px-2 text-sm border-0 outline-none bg-white"
                    style={{ fontSize: field.fontSize }}
                    autoFocus
                    placeholder="Type here..."
                  />
                ) : (
                  <div
                    className={`w-full h-full px-2 flex items-center text-sm cursor-text hover:bg-blue-50 bg-white ${
                      !field.text ? 'border-dashed' : ''
                    }`}
                    style={{ fontSize: field.fontSize }}
                    onClick={(e) => {
                      e.stopPropagation();
                      updateTextField(field.id, { isEditing: true });
                    }}
                  >
                    {field.text || (
                      <span className="text-gray-400 italic text-xs">
                        Click to add text
                      </span>
                    )}
                  </div>
                )}

                {/* Field Controls */}
                <div className="absolute -top-7 -right-1 flex gap-1 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity">
                  <button
                    className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-blue-600 shadow-md"
                    onClick={(e) => {
                      e.stopPropagation();
                      updateTextField(field.id, { isEditing: true });
                    }}
                    title="Edit text"
                  >
                    ✏️
                  </button>
                  <button
                    className="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 shadow-md"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteTextField(field.id);
                    }}
                    title="Delete field"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}

            {/* Enhanced Instructions Overlay */}
            <div className="absolute top-4 right-4 bg-green-100 border border-green-300 rounded-lg p-3 text-sm text-green-800 z-20 max-w-xs shadow-lg">
              <p className="font-medium mb-2">✏️ DocuSign-Style Editing</p>
              <ul className="text-xs space-y-1">
                <li>• Click "Add Text" button above</li>
                <li>• Click anywhere on the green overlay to add text</li>
                <li>• Click existing text fields to edit them</li>
                <li>• Hover over fields to see edit/delete buttons</li>
                <li>• Use built-in PDF fields if available</li>
              </ul>
            </div>

            {/* Add Text Mode Indicator */}
            {isAddingText && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-600 text-white px-6 py-3 rounded-lg shadow-xl z-40 pointer-events-none animate-pulse">
                <p className="font-medium text-center">
                  📝 Click anywhere on the green area to add a text field
                </p>
                <p className="text-xs mt-1 text-center opacity-90">
                  Press ESC to cancel • Click "Add Text" again to stop
                </p>
              </div>
            )}
          </div>

          {!originalPdfUrl && !isLoading && (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>privileges.pdf will load from lib folder when modal opens</p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};
