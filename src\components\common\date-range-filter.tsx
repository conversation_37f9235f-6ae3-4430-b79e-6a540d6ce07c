'use client';

import React, { useState, useEffect } from 'react';
import {
  Calendar as CalendarIcon,
  X,
  CalendarDays,
  Calendar1,
} from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

type DateMode = 'single' | 'range';

interface DateRangeFilterProps {
  // Original interface
  onDateRangeChange?: (
    startDate: Date | undefined,
    endDate: Date | undefined
  ) => void;
  // Single date interface
  onSingleDateChange?: (singleDate: Date | null) => void;
  // New interface for URL-based filtering
  startDate?: Date | null;
  endDate?: Date | null;
  singleDate?: Date | null;
  onStartDateChange?: (date: Date | null) => void;
  onEndDateChange?: (date: Date | null) => void;
  onClear?: () => void;
  // URL-based filter props
  onFilterChange?: (startDate: Date | null, endDate: Date | null) => void;
  onSingleFilterChange?: (singleDate: Date | null) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  initialSingleDate?: Date | null;
  className?: string;
  // New props for single date vs date range mode
  defaultMode?: DateMode;
  allowModeSwitch?: boolean;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  onDateRangeChange,
  onSingleDateChange,
  startDate: externalStartDate,
  endDate: externalEndDate,
  singleDate: externalSingleDate,
  onStartDateChange,
  onEndDateChange,
  onClear: externalClear,
  onFilterChange,
  onSingleFilterChange,
  initialStartDate,
  initialEndDate,
  initialSingleDate,
  className,
  defaultMode = 'range',
  allowModeSwitch = true,
}) => {
  // Use internal state if external state is not provided
  const [internalStartDate, setInternalStartDate] = useState<Date | undefined>(
    initialStartDate || undefined
  );
  const [internalEndDate, setInternalEndDate] = useState<Date | undefined>(
    initialEndDate || undefined
  );
  const [internalSingleDate, setInternalSingleDate] = useState<
    Date | undefined
  >(initialSingleDate || undefined);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [dateMode, setDateMode] = useState<DateMode>(defaultMode);

  // Determine if we're using internal, external, or URL-based state management
  const isExternalState =
    onStartDateChange !== undefined && onEndDateChange !== undefined;
  const isUrlBasedState = onFilterChange !== undefined;
  const isSingleDateState = onSingleDateChange !== undefined;
  const isUrlBasedSingleState = onSingleFilterChange !== undefined;

  // Get the current dates based on mode and state management approach
  const startDate =
    dateMode === 'single'
      ? undefined
      : isExternalState
        ? (externalStartDate as Date | undefined)
        : isUrlBasedState
          ? (initialStartDate as Date | undefined)
          : internalStartDate;

  const endDate =
    dateMode === 'single'
      ? undefined
      : isExternalState
        ? (externalEndDate as Date | undefined)
        : isUrlBasedState
          ? (initialEndDate as Date | undefined)
          : internalEndDate;

  const singleDate =
    dateMode === 'single'
      ? isSingleDateState && externalSingleDate !== undefined
        ? (externalSingleDate as Date | undefined)
        : isUrlBasedSingleState
          ? (initialSingleDate as Date | undefined)
          : internalSingleDate
      : undefined;

  // Set start date function that works with all interfaces
  const setStartDate = (date: Date | undefined) => {
    if (isExternalState) {
      onStartDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      // For URL-based filtering, we don't update local state directly
      // The parent component will handle URL updates
    } else {
      setInternalStartDate(date);
    }
  };

  // Set end date function that works with all interfaces
  const setEndDate = (date: Date | undefined) => {
    if (isExternalState) {
      onEndDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      // For URL-based filtering, we don't update local state directly
      // The parent component will handle URL updates
    } else {
      setInternalEndDate(date);
    }
  };

  // Set single date function
  const setSingleDate = (date: Date | undefined) => {
    if (isSingleDateState) {
      onSingleDateChange?.(date as Date | null);
    } else if (isUrlBasedSingleState) {
      onSingleFilterChange?.(date as Date | null);
    } else {
      setInternalSingleDate(date);
    }
  };

  // Update parent component when date range changes (for original interface)
  useEffect(() => {
    if (onDateRangeChange) {
      onDateRangeChange(startDate, endDate);
    }
  }, [startDate, endDate, onDateRangeChange]);

  // Handle date selection
  const handleSelect = (date: Date | undefined) => {
    if (dateMode === 'single') {
      // Single date mode - use single date handlers
      setSingleDate(date);
      setIsCalendarOpen(false);
    } else {
      // Range mode - existing logic
      if (!startDate || (startDate && endDate)) {
        // If no start date is selected or both dates are already selected, set start date
        if (isUrlBasedState) {
          onFilterChange?.(date || null, null);
        } else {
          setStartDate(date);
          setEndDate(undefined);
        }
      } else {
        // If only start date is selected and the new date is after start date, set end date
        if (date && date >= startDate) {
          if (isUrlBasedState) {
            onFilterChange?.(startDate, date);
          } else {
            setEndDate(date);
          }
          setIsCalendarOpen(false); // Close calendar after selecting end date
        } else {
          // If new date is before start date, swap them
          if (isUrlBasedState) {
            onFilterChange?.(date || null, startDate);
          } else {
            setEndDate(startDate);
            setStartDate(date);
          }
          setIsCalendarOpen(false); // Close calendar after selecting end date
        }
      }
    }
  };

  // Clear date range
  const handleClear = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    if (dateMode === 'single') {
      setSingleDate(undefined);
    } else {
      if (isExternalState) {
        externalClear?.();
      } else if (isUrlBasedState) {
        onFilterChange?.(null, null);
      } else {
        setStartDate(undefined);
        setEndDate(undefined);
      }
    }
  };

  // Format date range for display
  const formatDateRange = () => {
    if (dateMode === 'single') {
      if (singleDate) {
        return format(singleDate, 'MMM d, yyyy');
      }
      return 'Select date';
    } else {
      if (startDate && endDate) {
        return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
      }
      if (startDate) {
        return `${format(startDate, 'MMM d, yyyy')} - Select end date`;
      }
      return 'Select date range';
    }
  };

  return (
    <div className={cn('relative', className)}>
      {/* Main Date Filter Popover */}
      <div className="relative flex">
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal pr-8',
                !startDate && !singleDate && 'text-muted-foreground'
              )}
            >
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>{formatDateRange()}</span>
              </div>
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0"
            align="start"
            side="bottom"
            sideOffset={4}
            alignOffset={0}
            avoidCollisions={true}
            collisionPadding={8}
          >
            {/* Mode switcher header */}
            {allowModeSwitch && (
              <div className="p-3 border-b border-border">
                <div className="flex gap-1">
                  <Button
                    variant={dateMode === 'single' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-7 px-3 text-xs"
                    onClick={() => {
                      setDateMode('single');
                      // When switching to single mode, clear range dates and set single date to start date if available
                      if (startDate) {
                        setSingleDate(startDate);
                      }
                      // Clear range dates
                      if (isUrlBasedState) {
                        onFilterChange?.(null, null);
                      } else {
                        setStartDate(undefined);
                        setEndDate(undefined);
                      }
                    }}
                  >
                    <Calendar1 className="h-3 w-3 mr-1" />
                    Single
                  </Button>
                  <Button
                    variant={dateMode === 'range' ? 'default' : 'ghost'}
                    size="sm"
                    className="h-7 px-3 text-xs"
                    onClick={() => {
                      setDateMode('range');
                      // When switching to range mode, clear single date and set start date if available
                      if (singleDate) {
                        if (isUrlBasedState) {
                          onFilterChange?.(singleDate, null);
                        } else {
                          setStartDate(singleDate);
                        }
                      }
                      // Clear single date
                      if (isSingleDateState) {
                        onSingleDateChange?.(null);
                      } else if (isUrlBasedSingleState) {
                        onSingleFilterChange?.(null);
                      } else {
                        setSingleDate(undefined);
                      }
                    }}
                  >
                    <CalendarDays className="h-3 w-3 mr-1" />
                    Range
                  </Button>
                </div>
              </div>
            )}

            <Calendar
              mode="single"
              selected={
                dateMode === 'single' ? singleDate : endDate || startDate
              }
              onSelect={handleSelect}
              defaultMonth={dateMode === 'single' ? singleDate : startDate}
              numberOfMonths={1}
              disabled={(date) => {
                // Disable dates more than 1 year in the past or future
                const oneYearAgo = new Date();
                oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                const oneYearFromNow = new Date();
                oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
                return date < oneYearAgo || date > oneYearFromNow;
              }}
            />
            <div className="p-3 border-t border-border">
              <div className="flex justify-between items-center">
                <div className="text-sm">
                  {dateMode === 'single' ? (
                    singleDate && (
                      <span className="font-medium">
                        {format(singleDate, 'MMM d, yyyy')}
                      </span>
                    )
                  ) : (
                    <>
                      {startDate && (
                        <span className="font-medium">
                          {format(startDate, 'MMM d, yyyy')}
                        </span>
                      )}
                      {startDate && endDate && <span> - </span>}
                      {endDate && (
                        <span className="font-medium">
                          {format(endDate, 'MMM d, yyyy')}
                        </span>
                      )}
                    </>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleClear()}
                  className="h-7 px-3"
                >
                  Clear
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {(startDate || endDate || singleDate) && (
          <X
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 opacity-70 hover:opacity-100 cursor-pointer z-10"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleClear();
            }}
          />
        )}
      </div>
    </div>
  );
};

export default DateRangeFilter;
